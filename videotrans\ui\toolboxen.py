# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.configure.config import box_lang


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        self.has_done = False

        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.centralwidget)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")

        self.horizontalLayout_11 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout()
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.hecheng_layout = QtWidgets.QVBoxLayout()
        self.hecheng_layout.setObjectName("hecheng_layout")
        self.verticalLayout_4.addLayout(self.hecheng_layout)
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.horizontalLayout_10_1 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10_1.setObjectName("horizontalLayout_10_1")
        self.formLayout_3 = QtWidgets.QFormLayout()
        self.formLayout_3.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_3.setObjectName("formLayout_3")
        self.label_10 = QtWidgets.QLabel()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setMinimumSize(QtCore.QSize(0, 30))
        self.label_10.setObjectName("label_10")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_10)
        self.hecheng_language = QtWidgets.QComboBox()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_language.sizePolicy().hasHeightForWidth())
        self.hecheng_language.setSizePolicy(sizePolicy)
        self.hecheng_language.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_language.setObjectName("hecheng_language")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_language)
        self.horizontalLayout_10.addLayout(self.formLayout_3)
        self.formLayout_7 = QtWidgets.QFormLayout()
        self.formLayout_7.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_7.setObjectName("formLayout_7")
        self.label_8 = QtWidgets.QLabel()
        self.label_8.setMinimumSize(QtCore.QSize(0, 30))
        self.label_8.setObjectName("label_8")
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_8)
        self.tts_type = QtWidgets.QComboBox()
        self.tts_type.setMinimumSize(QtCore.QSize(0, 30))
        self.tts_type.setObjectName("tts_type")
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.tts_type)
        self.horizontalLayout_10.addLayout(self.formLayout_7)
        self.formLayout_4 = QtWidgets.QFormLayout()
        self.formLayout_4.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_4.setObjectName("formLayout_4")
        self.label_11 = QtWidgets.QLabel()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy)
        self.label_11.setMinimumSize(QtCore.QSize(0, 30))
        self.label_11.setObjectName("label_11")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_11)
        self.hecheng_role = QtWidgets.QComboBox()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_role.sizePolicy().hasHeightForWidth())
        self.hecheng_role.setSizePolicy(sizePolicy)
        self.hecheng_role.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_role.setObjectName("hecheng_role")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_role)
        self.horizontalLayout_10.addLayout(self.formLayout_4)

        self.listen_btn = QtWidgets.QPushButton()
        self.listen_btn.setFixedWidth(80)
        self.listen_btn.setToolTip(config.uilanglist.get("shuoming01"))
        self.listen_btn.setText(config.uilanglist.get("Trial dubbing"))
        self.horizontalLayout_10.addWidget(self.listen_btn)

        self.formLayout_5 = QtWidgets.QFormLayout()
        self.formLayout_5.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout_5.setObjectName("formLayout_5")
        self.label_12 = QtWidgets.QLabel()
        self.label_12.setMinimumSize(QtCore.QSize(0, 30))
        self.label_12.setObjectName("label_12")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_12)
        self.hecheng_rate = QtWidgets.QSpinBox()
        self.hecheng_rate.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_rate.setMinimum(-100)
        self.hecheng_rate.setMaximum(100)
        self.hecheng_rate.setObjectName("hecheng_rate")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.hecheng_rate)
        self.horizontalLayout_10_1.addLayout(self.formLayout_5)

        self.tts_issrt = QtWidgets.QCheckBox()
        self.tts_issrt.setObjectName("tts_issrt")

        self.voice_autorate = QtWidgets.QCheckBox()
        self.voice_autorate.setEnabled(False)
        self.voice_autorate.setObjectName("voice_autorate")

        self.horizontalLayout_10_1.addWidget(self.tts_issrt)
        self.horizontalLayout_10_1.addWidget(self.voice_autorate)

        self.edge_volume_layout = QtWidgets.QHBoxLayout()

        self.volume_label = QtWidgets.QLabel()
        self.volume_label.setText("音量+" if config.defaulelang == 'zh' else "Volume+")

        self.volume_rate = QtWidgets.QSpinBox()
        self.volume_rate.setMinimum(-95)
        self.volume_rate.setMaximum(100)
        self.volume_rate.setObjectName("volume_rate")

        self.pitch_label = QtWidgets.QLabel()
        self.pitch_label.setText("音调+" if config.defaulelang == 'zh' else "Pitch+")
        self.pitch_rate = QtWidgets.QSpinBox()
        self.pitch_rate.setMinimum(-100)
        self.pitch_rate.setMaximum(100)
        self.pitch_rate.setObjectName("pitch_rate")

        self.edge_volume_layout.addWidget(self.volume_label)
        self.edge_volume_layout.addWidget(self.volume_rate)
        self.edge_volume_layout.addWidget(self.pitch_label)
        self.edge_volume_layout.addWidget(self.pitch_rate)

        self.horizontalLayout_10_1.addLayout(self.edge_volume_layout)
        self.hecheng_startbtn = QtWidgets.QPushButton()
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.hecheng_startbtn.sizePolicy().hasHeightForWidth())
        self.hecheng_startbtn.setSizePolicy(sizePolicy)
        self.hecheng_startbtn.setMinimumSize(QtCore.QSize(200, 40))
        self.hecheng_startbtn.setObjectName("hecheng_startbtn")
        self.hecheng_startbtn.setCursor(Qt.PointingHandCursor)

        self.verticalLayout_4.addLayout(self.horizontalLayout_10)
        self.verticalLayout_4.addLayout(self.horizontalLayout_10_1)

        self.verticalLayout_4.addWidget(self.hecheng_startbtn)
        self.gridLayout_3 = QtWidgets.QGridLayout()
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.label_7 = QtWidgets.QLabel()
        self.label_7.setMinimumSize(QtCore.QSize(100, 30))
        self.label_7.setObjectName("label_7")
        self.gridLayout_3.addWidget(self.label_7, 0, 0, 1, 1)
        self.hecheng_out = QtWidgets.QLineEdit()
        self.hecheng_out.setMinimumSize(QtCore.QSize(0, 30))
        self.hecheng_out.setReadOnly(False)
        self.hecheng_out.setObjectName("hecheng_out")
        self.gridLayout_3.addWidget(self.hecheng_out, 0, 1, 1, 1)
        self.hecheng_opendir = QtWidgets.QPushButton()
        self.hecheng_opendir.setObjectName("hecheng_opendir")
        self.gridLayout_3.addWidget(self.hecheng_opendir, 0, 2, 1, 1)
        self.verticalLayout_4.addLayout(self.gridLayout_3)
        self.horizontalLayout_11.addLayout(self.verticalLayout_4)
        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(box_lang.get("Video Toolbox"))

        self.label_10.setText(box_lang.get("Subtitle lang"))
        self.label_8.setText("TTS" if config.defaulelang != 'zh' else '配音渠道')
        self.label_11.setText(box_lang.get("Select role"))
        self.label_12.setText(box_lang.get("Speed change"))
        self.hecheng_rate.setToolTip(box_lang.get("Negative deceleration, positive acceleration"))
        self.tts_issrt.setToolTip(box_lang.get("If so, the line number and time value will skip reading aloud"))
        self.tts_issrt.setText(box_lang.get("Is srt?"))
        self.voice_autorate.setText(box_lang.get("Automatic acceleration?"))
        self.hecheng_startbtn.setText(box_lang.get("Start"))
        self.label_7.setText(box_lang.get("Output audio name"))
        self.hecheng_out.setPlaceholderText(box_lang.get(
            "Set the name of the generated audio file here. If not filled in, use the time and date command"))
        self.hecheng_opendir.setText(box_lang.get("Open dir"))
