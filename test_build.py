#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để test build PyVideoTrans trước khi build ch<PERSON>h thức
Test build script for PyVideoTrans before official build
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def test_imports():
    """Test tất cả imports quan trọng"""
    print("🧪 Testing critical imports...")
    
    critical_imports = [
        ('PySide6.QtCore', 'PySide6'),
        ('PySide6.QtGui', 'PySide6'),
        ('PySide6.QtWidgets', 'PySide6'),
        ('videotrans', 'videotrans'),
        ('videotrans.configure.config', 'videotrans.configure'),
        ('numpy', 'numpy'),
        ('requests', 'requests'),
        ('torch', 'torch'),
        ('faster_whisper', 'faster-whisper'),
        ('edge_tts', 'edge-tts'),
        ('pydub', 'pydub'),
        ('librosa', 'librosa'),
        ('soundfile', 'soundfile'),
        ('PIL', 'Pillow'),
        ('srt', 'srt'),
        ('zhconv', 'zhconv'),
    ]
    
    failed_imports = []
    
    for import_name, package_name in critical_imports:
        try:
            __import__(import_name)
            print(f"✅ {import_name}")
        except ImportError as e:
            print(f"❌ {import_name}: {e}")
            failed_imports.append((import_name, package_name))
    
    if failed_imports:
        print(f"\n⚠️  Failed imports: {len(failed_imports)}")
        for import_name, package_name in failed_imports:
            print(f"   - {import_name} (install: pip install {package_name})")
        return False
    
    print(f"✅ All {len(critical_imports)} critical imports successful!")
    return True

def test_videotrans_structure():
    """Test cấu trúc thư mục videotrans"""
    print("\n🧪 Testing videotrans structure...")
    
    required_paths = [
        'videotrans/__init__.py',
        'videotrans/configure/__init__.py',
        'videotrans/configure/config.py',
        'videotrans/mainwin/__init__.py',
        'videotrans/mainwin/_main_win.py',
        'videotrans/ui/dark/darkstyle_rc.py',
        'videotrans/styles/icon.ico',
        'videotrans/styles/logo.png',
        'videotrans/styles/style.qss',
        'videotrans/language/zh.json',
        'videotrans/language/en.json',
        'sp.py',
    ]
    
    missing_paths = []
    
    for path in required_paths:
        if Path(path).exists():
            print(f"✅ {path}")
        else:
            print(f"❌ {path}")
            missing_paths.append(path)
    
    if missing_paths:
        print(f"\n⚠️  Missing files: {len(missing_paths)}")
        for path in missing_paths:
            print(f"   - {path}")
        return False
    
    print(f"✅ All {len(required_paths)} required files found!")
    return True

def test_entry_point():
    """Test entry point sp.py"""
    print("\n🧪 Testing entry point...")
    
    try:
        # Test import của sp.py
        import sp
        print("✅ sp.py imports successfully")
        
        # Test các imports trong sp.py
        from PySide6 import QtWidgets
        from PySide6.QtCore import Qt, QTimer, QPoint, QSettings, QSize
        from PySide6.QtGui import QPixmap, QIcon, QGuiApplication
        from videotrans import VERSION
        
        print("✅ All sp.py dependencies available")
        print(f"✅ PyVideoTrans version: {VERSION}")
        
        return True
    except Exception as e:
        print(f"❌ Entry point test failed: {e}")
        return False

def test_pyinstaller_compatibility():
    """Test PyInstaller compatibility"""
    print("\n🧪 Testing PyInstaller compatibility...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
        
        # Test PyInstaller với một script đơn giản
        test_script = '''
import sys
from PySide6.QtWidgets import QApplication, QLabel
from videotrans import VERSION

app = QApplication(sys.argv)
label = QLabel(f"PyVideoTrans {VERSION}")
print("Test successful!")
app.quit()
'''
        
        # Tạo file test tạm thời
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            test_file = f.name
        
        try:
            # Test PyInstaller analysis
            cmd = [
                sys.executable, '-m', 'PyInstaller',
                '--onefile',
                '--windowed',
                '--noconfirm',
                '--distpath', tempfile.gettempdir(),
                '--workpath', tempfile.gettempdir(),
                '--specpath', tempfile.gettempdir(),
                test_file
            ]
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            
            if result.returncode == 0:
                print("✅ PyInstaller compatibility test passed")
                return True
            else:
                print(f"❌ PyInstaller test failed: {result.stderr}")
                return False
                
        finally:
            # Cleanup
            try:
                os.unlink(test_file)
            except:
                pass
                
    except Exception as e:
        print(f"❌ PyInstaller compatibility test failed: {e}")
        return False

def test_spec_file():
    """Test file .spec"""
    print("\n🧪 Testing spec file...")
    
    spec_file = Path('pyvideotrans.spec')
    if not spec_file.exists():
        print("❌ pyvideotrans.spec not found")
        return False
    
    try:
        # Đọc và validate spec file
        with open(spec_file, 'r', encoding='utf-8') as f:
            spec_content = f.read()
        
        # Kiểm tra các phần quan trọng
        required_sections = [
            'Analysis(',
            'PYZ(',
            'EXE(',
            'COLLECT(',
            'hiddenimports',
            'datas',
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in spec_content:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ Missing sections in spec file: {missing_sections}")
            return False
        
        print("✅ Spec file structure is valid")
        
        # Test syntax
        try:
            compile(spec_content, spec_file, 'exec')
            print("✅ Spec file syntax is valid")
            return True
        except SyntaxError as e:
            print(f"❌ Spec file syntax error: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error reading spec file: {e}")
        return False

def create_test_report():
    """Tạo báo cáo test"""
    print("\n📊 Creating test report...")
    
    tests = [
        ("Critical Imports", test_imports),
        ("Project Structure", test_videotrans_structure),
        ("Entry Point", test_entry_point),
        ("Spec File", test_spec_file),
        ("PyInstaller Compatibility", test_pyinstaller_compatibility),
    ]
    
    results = {}
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
            all_passed = False
    
    # Tạo báo cáo
    print("\n" + "="*60)
    print("📊 TEST REPORT")
    print("="*60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("="*60)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Ready to build with PyInstaller")
        print("\nNext steps:")
        print("1. Run: python build_exe.py")
        print("2. Or run: build.bat")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️  Please fix the issues above before building")
        print("\nSuggested fixes:")
        print("1. Run: python fix_build_issues.py")
        print("2. Install missing dependencies")
        print("3. Check file paths and structure")
    
    return all_passed

def main():
    """Main test function"""
    print("🧪 PyVideoTrans Build Test Suite")
    print("=" * 60)
    
    # Kiểm tra Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major != 3 or python_version.minor < 8:
        print("❌ Python 3.8+ required")
        return False
    
    # Kiểm tra working directory
    if not Path('sp.py').exists():
        print("❌ Please run this script from the PyVideoTrans root directory")
        return False
    
    # Chạy tất cả tests
    return create_test_report()

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
