# -*- coding: utf-8 -*-

import re
import socket
import time
from typing import Union, List
from pathlib import Path
import requests
import google
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from google.api_core.exceptions import ServerError,TooManyRequests,RetryError
from videotrans.configure import config
from videotrans.translator._base import BaseTrans
from videotrans.util import tools

safetySettings = {
    'HATE': 'BLOCK_NONE',
    'HARASSMENT': 'BLOCK_NONE',
    'SEXUAL' : 'BLOCK_NONE',
    'DANGEROUS' : 'BLOCK_NONE'
}


# 代理修改  site-packages\google\ai\generativelanguage_v1beta\services\generative_service\transports\grpc_asyncio.py __init__方法的 options 添加 ("grpc.http_proxy",os.environ.get('http_proxy') or os.environ.get('https_proxy'))
class Gemini(BaseTrans):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.trans_thread=int(config.settings.get('aitrans_thread',50))
        self._set_proxy(type='set')
        # Force sử dụng prompt tiếng Anh cho Gemini
        self.prompt = self._get_english_prompt().replace('{lang}', self.target_language_name)
        self.model_name=config.params["gemini_model"]

        self.api_keys=config.params.get('gemini_key','').strip().split(',')

    def _get_english_prompt(self):
        """Force sử dụng prompt tiếng Anh cho Gemini"""
        prompt_path = f'{config.ROOT_DIR}/videotrans/'
        prompt_name = f'gemini-en.txt'  # Force sử dụng prompt tiếng Anh
        if self.is_srt and config.settings.get('aisendsrt', False):
            prompt_path += 'prompts/srt/'

        prompt_file = f'{prompt_path}{prompt_name}'

        # Nếu file không tồn tại, tạo file mặc định
        if not Path(prompt_file).exists():
            self._create_default_english_prompt(prompt_file)

        return Path(prompt_file).read_text(encoding='utf-8')

    def _create_default_english_prompt(self, prompt_file):
        """Tạo file prompt tiếng Anh mặc định"""
        default_prompt = """# Role:
You are a professional video subtitle translation specialist, dedicated to translating subtitle content into {lang}, ensuring natural fluency and adherence to target language expression conventions.

## Core Mission:
Translate the input subtitle text completely and accurately into {lang}, maintaining the original timeline and format structure.

## Translation Rules:
- **MUST TRANSLATE EVERYTHING**: Absolutely no original text should remain untranslated, even proper nouns must be given the best {lang} expression
- **STRICT LINE CORRESPONDENCE**: One source line corresponds to one translated line, never merge or split lines
- **NATURAL COLLOQUIAL**: Use natural expressions that conform to daily {lang} speech, avoid rigid literal translation
- **CONCISE AND CLEAR**: Subtitle translation should be concise and easy to understand, avoid lengthy complex sentences
- **MAINTAIN CONTEXT**: Consider contextual coherence to ensure translation continuity

## Special Handling:
- **Names & Places**: Transliterate to common {lang} translations, or phonetically translate if no standard exists
- **Technical Terms**: Use standard terminology within the {lang} field
- **Cultural Elements**: Appropriately localize while preserving original meaning
- **Tone & Mood**: Maintain the emotional color and tone of the original text

## Quality Standards:
- Translation must be complete with no omissions
- Language should be natural and fluent, conforming to {lang} expression habits
- Meaning must be accurate without misinterpretation
- Format must be standardized, strictly following output requirements

## Output Format:
**MUST strictly follow the XML format below, do not add any other content:**

```xml
<TRANSLATE_TEXT>
Translated {lang} text
Each line corresponds to one line of the original
Maintain the same number of lines
</TRANSLATE_TEXT>
```

## Important Reminders:
- 🚫 Absolutely forbidden to return original text or mixed languages
- 🚫 Forbidden to add explanations, notes, or other content
- ✅ Only return pure translation results in XML format
- ✅ Ensure complete line correspondence

## Input Content:
<INPUT></INPUT>"""

        # Tạo thư mục nếu chưa tồn tại
        Path(prompt_file).parent.mkdir(parents=True, exist_ok=True)

        # Ghi file
        with Path(prompt_file).open('w', encoding='utf-8') as f:
            f.write(default_prompt)

    def _item_task(self, data: Union[List[str], str]) -> str:
        if self.refine3:
            return self._item_task_refine3(data)

        # Retry logic for XML format validation
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                response = None
                text="\n".join([i.strip() for i in data]) if isinstance(data,list) else data
                message = self.prompt.replace('<INPUT></INPUT>',f'<INPUT>{text}</INPUT>')

                # Add retry-specific instructions if this is a retry
                if retry_count > 0:
                    retry_instruction = (
                        "\n\n⚠️ IMPORTANT: You MUST return your translation wrapped in <TRANSLATE_TEXT></TRANSLATE_TEXT> XML tags. "
                        "Do not include any other text outside these tags. Example:\n"
                        "<TRANSLATE_TEXT>\nYour translation here\n</TRANSLATE_TEXT>"
                    ) if config.defaulelang != 'zh' else (
                        "\n\n⚠️ 重要提醒：您必须将翻译结果包装在 <TRANSLATE_TEXT></TRANSLATE_TEXT> XML标签中。"
                        "不要在这些标签之外包含任何其他文本。示例：\n"
                        "<TRANSLATE_TEXT>\n您的翻译内容\n</TRANSLATE_TEXT>"
                    )
                    message += retry_instruction

                api_key=self.api_keys.pop(0)
                self.api_keys.append(api_key)
                config.logger.info(f'[Gemini]请求发送(尝试{retry_count+1}/{max_retries}):{api_key=},{config.params["gemini_model"]=}')
                genai.configure(api_key=api_key)

                model = genai.GenerativeModel(
                    model_name=config.params['gemini_model'],
                    generation_config={"max_output_tokens": 8192},
                    system_instruction="You are a translation assistant specializing in converting SRT subtitle content from one language to another while maintaining the original format and structure." if config.defaulelang != 'zh' else '您是一名翻译助理，专门负责将 SRT 字幕内容从一种语言转换为另一种语言，同时保持原始格式和结构。'
                )
                response = model.generate_content(
                    message,
                    safety_settings=safetySettings
                )

                # Check if response has candidates
                if not response.candidates:
                    error_msg = "Response candidates is empty - content may be blocked by safety filters"
                    config.logger.warning(f'[Gemini]{error_msg}')

                    # Check if there's a prompt feedback
                    if hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                        if hasattr(response.prompt_feedback, 'block_reason'):
                            block_reason = response.prompt_feedback.block_reason
                            error_msg = f"Content blocked by safety filters: {block_reason}"
                            config.logger.error(f'[Gemini]{error_msg}')

                    if retry_count < max_retries - 1:
                        config.logger.warning(f'[Gemini]Empty candidates, retrying with modified prompt (attempt {retry_count+1}/{max_retries})')
                        retry_count += 1
                        continue
                    else:
                        raise Exception(f"Gemini safety filters blocked the content after {max_retries} attempts" if config.defaulelang != 'zh' else f"Gemini安全过滤器阻止了内容，已重试{max_retries}次")

                # Check if response.text is accessible
                try:
                    result = response.text
                except Exception as e:
                    error_msg = f"Cannot access response.text: {str(e)}"
                    config.logger.warning(f'[Gemini]{error_msg}')

                    if retry_count < max_retries - 1:
                        config.logger.warning(f'[Gemini]Cannot access response text, retrying (attempt {retry_count+1}/{max_retries})')
                        retry_count += 1
                        continue
                    else:
                        raise Exception(f"Cannot access Gemini response text after {max_retries} attempts: {str(e)}" if config.defaulelang != 'zh' else f"无法访问Gemini响应文本，已重试{max_retries}次: {str(e)}")

                config.logger.info(f'[Gemini]返回(尝试{retry_count+1}):{result=}')

                if not result:
                    if retry_count < max_retries - 1:
                        config.logger.warning(f'[Gemini]Empty result, retrying (attempt {retry_count+1}/{max_retries})')
                        retry_count += 1
                        continue
                    else:
                        raise Exception("result is empty after all retries")

                # Try to extract XML content
                match = re.search(r'<TRANSLATE_TEXT>(.*?)</TRANSLATE_TEXT>', response.text, re.S)
                if match:
                    extracted_text = match.group(1).strip()
                    config.logger.info(f'[Gemini]成功提取XML内容:{extracted_text=}')
                    return extracted_text

                # If no XML tags found, check if this is a retry attempt
                if retry_count < max_retries - 1:
                    config.logger.warning(f'[Gemini]未找到XML标签，准备重试(尝试{retry_count+1}/{max_retries})')
                    retry_count += 1
                    continue
                else:
                    # Last attempt failed, try to clean and return the raw text
                    config.logger.warning(f'[Gemini]所有重试失败，返回原始文本')
                    cleaned_text = self._clean_raw_response(response.text, text)
                    return cleaned_text

            except TooManyRequests as e:
                if retry_count < max_retries - 1:
                    config.logger.warning(f'[Gemini]429错误，准备重试(尝试{retry_count+1}/{max_retries})')
                    retry_count += 1
                    time.sleep(60)  # Wait 60 seconds for rate limit
                    continue
                else:
                    raise Exception('429超过请求次数，请尝试更换其他Gemini模型后重试' if config.defaulelang=='zh' else 'Too many requests, use other model retry')
            except (ServerError, RetryError, socket.timeout) as e:
                if retry_count < max_retries - 1:
                    config.logger.warning(f'[Gemini]连接错误，准备重试(尝试{retry_count+1}/{max_retries}):{str(e)}')
                    retry_count += 1
                    time.sleep(5)  # Wait 5 seconds for connection issues
                    continue
                else:
                    error = str(e) if config.defaulelang != 'zh' else '无法连接到Gemini,请尝试使用或更换代理或切换模型'
                    raise requests.ConnectionError(error)
            except google.api_core.exceptions.PermissionDenied:
                raise Exception('您无权访问所请求的资源或模型' if config.defaulelang =='zh' else 'You donot have permission for the requested resource')
            except google.api_core.exceptions.ResourceExhausted:
                raise Exception(f'您的配额已用尽。请稍等片刻，然后重试,若仍如此，请查看Google账号 ' if config.defaulelang =='zh' else 'Your quota is exhausted. Please wait a bit and try again')
            except google.auth.exceptions.DefaultCredentialsError:
                raise Exception(f'验证失败，可能 Gemini API Key 不正确 ' if config.defaulelang =='zh' else 'Authentication fails. Please double-check your API key and try again')
            except google.api_core.exceptions.InvalidArgument as e:
                config.logger.exception(e, exc_info=True)
                raise Exception(f'文件过大或 Gemini API Key 不正确 {e}' if config.defaulelang =='zh' else f'Invalid argument. One example is the file is too large and exceeds the payload size limit. Another is providing an invalid API key {e}')
            except google.api_core.exceptions.RetryError:
                raise Exception('无法连接到Gemini，请尝试使用或更换代理' if config.defaulelang=='zh' else 'Can be caused when using a proxy that does not support gRPC.')
            except genai.types.BlockedPromptException as e:
                raise Exception(self._get_error(e.args[0].finish_reason))
            except genai.types.StopCandidateException as e:
                config.logger.exception(e, exc_info=True)
                if int(e.args[0].finish_reason>1):
                    raise Exception(self._get_error(e.args[0].finish_reason))
            except Exception as e:
                if retry_count < max_retries - 1:
                    config.logger.warning(f'[Gemini]未知错误，准备重试(尝试{retry_count+1}/{max_retries}):{str(e)}')
                    retry_count += 1
                    time.sleep(2)  # Wait 2 seconds for unknown errors
                    continue
                else:
                    error = str(e)
                    config.logger.error(f'[Gemini]请求失败:{error=}')
                    if error.find('User location is not supported') > -1:
                        raise Exception("当前请求ip(或代理服务器)所在国家不在Gemini API允许范围")
                    raise e

        # This should never be reached, but just in case
        return response.text.strip() if response else ""

    def _clean_raw_response(self, raw_response: str, original_text: str) -> str:
        """
        Clean and process raw response when XML tags are not found
        尝试从原始响应中提取有用的翻译内容
        """
        try:
            # Remove common prefixes and suffixes that Gemini might add
            cleaned = raw_response.strip()

            # Remove common response patterns
            patterns_to_remove = [
                r'^(Here is the translation|这是翻译结果|翻译如下)[：:]\s*',
                r'^(Translation|翻译)[：:]\s*',
                r'```.*?```',  # Remove code blocks
                r'^\*\*.*?\*\*\s*',  # Remove bold markdown
                r'^#+\s*.*?\n',  # Remove markdown headers
            ]

            for pattern in patterns_to_remove:
                cleaned = re.sub(pattern, '', cleaned, flags=re.MULTILINE | re.DOTALL)

            # Split into lines and filter
            lines = [line.strip() for line in cleaned.split('\n') if line.strip()]

            # Try to match the number of lines from original text
            original_lines = [line.strip() for line in original_text.split('\n') if line.strip()]

            if len(lines) == len(original_lines):
                # Perfect match, return as is
                return '\n'.join(lines)
            elif len(lines) > len(original_lines):
                # Too many lines, take the first N lines
                return '\n'.join(lines[:len(original_lines)])
            elif len(lines) < len(original_lines) and len(lines) > 0:
                # Too few lines, duplicate the last line or use original for missing
                while len(lines) < len(original_lines):
                    if lines:
                        lines.append(lines[-1])  # Duplicate last translated line
                    else:
                        lines.append(original_lines[len(lines)])  # Use original
                return '\n'.join(lines)
            else:
                # No valid lines found, return original text
                config.logger.warning(f'[Gemini]无法从响应中提取有效内容，返回原文')
                return original_text

        except Exception as e:
            config.logger.error(f'[Gemini]清理响应时出错:{str(e)}，返回原文')
            return original_text

    def _validate_translation_quality(self, translation: str, original: str) -> bool:
        """
        Validate if the translation is of acceptable quality
        验证翻译质量是否可接受
        """
        try:
            # Basic checks
            if not translation or not translation.strip():
                return False

            # Check if translation is too similar to original (might not be translated)
            similarity_threshold = 0.8
            if len(translation) > 0 and len(original) > 0:
                # Simple character-based similarity check
                common_chars = sum(1 for a, b in zip(translation.lower(), original.lower()) if a == b)
                similarity = common_chars / max(len(translation), len(original))

                if similarity > similarity_threshold:
                    config.logger.warning(f'[Gemini]翻译与原文过于相似，可能未正确翻译')
                    return False

            # Check for common untranslated patterns
            untranslated_patterns = [
                r'^[a-zA-Z\s]+$',  # Only English letters and spaces
                r'^[\u4e00-\u9fff\s]+$',  # Only Chinese characters and spaces
            ]

            # If target language is not Chinese/English, these patterns might indicate untranslated text
            target_lang = self.target_language_name.lower()
            if target_lang not in ['chinese', 'english', 'zh', 'en', '中文', '英文']:
                for pattern in untranslated_patterns:
                    if re.match(pattern, translation.strip()):
                        config.logger.warning(f'[Gemini]检测到可能未翻译的内容模式')
                        return False

            return True

        except Exception as e:
            config.logger.error(f'[Gemini]验证翻译质量时出错:{str(e)}')
            return True  # Default to accepting the translation

    def _get_error(self, num=5, type='error'):
        REASON_CN = {
            2: "已达到请求中指定的最大令牌数量",
            3: "Gemini安全限制:候选响应内容被标记",
            4:"Gemini安全限制:候选响应内容因背诵原因被标记",
            5:"Gemini安全限制:原因不明",
            6:"Gemini安全限制:候选回应内容因使用不支持的语言而被标记",
            7:"Gemini安全限制:由于内容包含禁用术语，令牌生成停止",
            8:"Gemini安全限制:令牌生成因可能包含违禁内容而停止",
            9: "Gemini安全限制:令牌生成停止，因为内容可能包含敏感的个人身份信息",
            10: "模型生成的函数调用无效",
        }
        REASON_EN = {
            2: "The maximum number of tokens as specified in the request was reached",
            3: "The response candidate content was flagged for safety reasons",
            4: "The response candidate content was flagged  for recitation reasons",
            5: "Unknown reason",
            6:"The response candidate content was flagged for using an unsupported language",
            7:"Token generation stopped because the content contains forbidden terms",
            8:"Token generation stopped for potentially containing prohibited content",
            9:"Token generation stopped because the content potentially contains  Sensitive Personally Identifiable Information",
            10:"The function call generated by the model is invalid",
        }
        forbid_cn = {
            0: "Gemini安全限制::安全考虑",
            1: "Gemini安全限制::出于安全考虑，提示已被屏蔽",
            2: "Gemini安全限制:提示因未知原因被屏蔽了",
            3: "Gemini安全限制:提示因术语屏蔽名单中包含的字词而被屏蔽",
            4: "Gemini安全限制:系统屏蔽了此提示，因为其中包含禁止的内容。",
        }
        forbid_en = {
            0: "Prompt was blocked by gemini",
            1: "Prompt was blocked due to safety reasons",
            2: "Prompt was blocked due to unknown reasons",
            3:"Prompt was blocked due to the terms which are included from the terminology blocklist",
            4:"Prompt was blocked due to prohibited content."
        }
        if config.defaulelang == 'zh':
            return REASON_CN[num] if type == 'error' else forbid_cn[num]
        return REASON_EN[num] if type == 'error' else forbid_en[num]

    def _item_task_refine3(self, data: Union[List[str], str]) -> str:
        prompt=self._refine3_prompt()
        text="\n".join([i.strip() for i in data]) if isinstance(data,list) else data
        prompt=prompt.replace('{lang}',self.target_language_name).replace('<INPUT></INPUT>',f'<INPUT>{text}</INPUT>')

        response = None
        try:
            api_key=self.api_keys.pop(0)
            self.api_keys.append(api_key)
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel(config.params['gemini_model'], safety_settings=safetySettings)
            response = model.generate_content(
                prompt,
                safety_settings=safetySettings
            )
            config.logger.info(f'[Gemini]请求发送:{prompt=}')

            # Check if response has candidates
            if not response.candidates:
                error_msg = "Response candidates is empty in refine3 - content may be blocked"
                config.logger.error(f'[Gemini]{error_msg}')

                # Check prompt feedback
                if hasattr(response, 'prompt_feedback') and response.prompt_feedback:
                    if hasattr(response.prompt_feedback, 'block_reason'):
                        block_reason = response.prompt_feedback.block_reason
                        error_msg = f"Content blocked by safety filters in refine3: {block_reason}"
                        config.logger.error(f'[Gemini]{error_msg}')

                raise Exception(f"Gemini safety filters blocked the content in refine3" if config.defaulelang != 'zh' else f"Gemini安全过滤器在refine3阶段阻止了内容")

            # Check if response.text is accessible
            try:
                response_text = response.text
            except Exception as e:
                error_msg = f"Cannot access response.text in refine3: {str(e)}"
                config.logger.error(f'[Gemini]{error_msg}')
                raise Exception(f"Cannot access Gemini response text in refine3: {str(e)}" if config.defaulelang != 'zh' else f"无法访问Gemini响应文本在refine3阶段: {str(e)}")

            config.logger.info(f'[Gemini]返回:{response_text=}')
            match = re.search(r'<step3_refined_translation>(.*?)</step3_refined_translation>', response_text, re.S)
            if not match:
                match = re.search(r'<TRANSLATE_TEXT>(.*?)</TRANSLATE_TEXT>', re.sub(r'<think>(.*?)</think>','', response_text, re.S|re.I), re.S|re.I)
            if match:
                return match.group(1)
            return response_text.strip()
        except TooManyRequests as e:
            raise Exception('429超过请求次数，请尝试更换其他Gemini模型后重试' if config.defaulelang=='zh' else 'Too many requests, use other model retry')
        except (ServerError,RetryError,socket.timeout) as e:
            error=str(e) if config.defaulelang !='zh' else '无法连接到Gemini,请尝试使用或更换代理'
            raise requests.ConnectionError(error)
        except google.api_core.exceptions.PermissionDenied:
            raise Exception('您无权访问所请求的资源或模型' if config.defaulelang =='zh' else 'You donot have permission for the requested resource')
        except google.api_core.exceptions.ResourceExhausted:                
            raise Exception(f'您的配额已用尽。请稍等片刻，然后重试,若仍如此，请查看Google账号 ' if config.defaulelang =='zh' else 'Your quota is exhausted. Please wait a bit and try again')
        except google.auth.exceptions.DefaultCredentialsError:                
            raise Exception(f'验证失败，可能 Gemini API Key 不正确 ' if config.defaulelang =='zh' else 'Authentication fails. Please double-check your API key and try again')
        except google.api_core.exceptions.InvalidArgument:                
            raise Exception(f'文件过大或 Gemini API Key 不正确 ' if config.defaulelang =='zh' else 'Invalid argument. One example is the file is too large and exceeds the payload size limit. Another is providing an invalid API key')
        except google.api_core.exceptions.RetryError:
            raise Exception('无法连接到Gemini，请尝试使用或更换代理' if config.defaulelang=='zh' else 'Can be caused when using a proxy that does not support gRPC.')
        except genai.types.BlockedPromptException as e:
            raise Exception(self._get_error(e.args[0].finish_reason))
        except genai.types.StopCandidateException as e:
            if int(e.args[0].finish_reason>1):
                raise Exception(self._get_error(e.args[0].finish_reason))
        
        except Exception as e:
            error = str(e)
            config.logger.error(f'[Gemini]请求失败:{error=}')
            if error.find('User location is not supported') > -1:
                raise Exception("当前请求ip(或代理服务器)所在国家不在Gemini API允许范围")
            raise