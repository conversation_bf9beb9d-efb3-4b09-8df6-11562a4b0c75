{"translate_language": {"llmduanju": "If you choose the large model segmentation, you must set the available model information in the translation settings--OpenAI API", "Succeed": "Succeed", "haspaused": "Paused", "shutdownerror": "computer shutdown error", "onlycnanden": "ChatTTS only support Chinese and English", "peiyindayu31": "The number of dubbing errors is greater than 1/3, please check the", "chaochu255": "The original video path and name is too long, please shorten the video name and move it to a shorter path to avoid subsequent errors.", "teshufuhao": "Please do not include any special symbols such as + & ? : | etc. in the path or name of the original video to avoid subsequent errors.", "notjson": "Return response is not valid json data", "fanyicuowu2": "The number of translation errors is more than half, please check", "azureinfo": "Did you set the speech resource key and region values", "yuchulichucuo": "Error in preprocessing stage", "shibiechucuo": "Error in speech recognition", "fanyichucuo": "Error in subtitle translation", "peiyinchucuo": "Error in dubbing", "hebingchucuo": "Error in combine", "freechatgpt_tips": "apiskey.top sponsored ChatGPT, free to use", "yidaorujigewenjian": "Imported subtitle files:", "dakaizimubaocunmulu": "Open directory where translation saved", "quanbuwanbi": "All translated.", "srtgeshierror": "Error while formatting subtitle, please check if the subtitle file conforms to the srt format", "endopendir": "Click to open the save directory", "xinshoumoshitips": "Select the video you want to translate and set the original language of the video and the language you want to translate to", "enmodelerror": "Models with the .en suffix can only be used if the original language is English", "openaimodelerror": "Models starting with distil-whisper cannot be selected when using openai models.", "qiegeshujuhaoshi": "If the video is very large, this step will be time-consuming, so please be patient.", "youtubehasdown": "The download has begun, please check the progress in the pop-up download interface, WARNING: message do not need to pay attention to, just ignore it!", "starting...": "Processing will start and progress will be shown later", "dubbing speed up": "Dub speed up", "video speed down": "Video slowdown", "auto_ajust": "Voice Extension", "audio_concat": "Connecting audio clips", "auto_ajust_tooltips": "When this item is checked, if the voiceover is longer than the original duration, the immediately following mute clip will be taken up first in the backward direction\nIf there is still not enough time to fully place the voiceover, then the mute clip will be taken forward.\nAdjust according to whether the voiceover is automatically sped up or the video is automatically slowed down.", "tts tooltip": "Choose the dubbing channel, clone-voice/GPT-SoVITS must first fill in the API information in the upper-left menu - Settings.", "trans tooltip": "Select the translation channel to use, Google/Gemini/chatGPT official interface can not be connected to the domestic, you must fill in the right side of the network proxy address", "fenlinoviceerror": "Failed to separate novoice.mp4.", "No subtitles file": "No subtitle file", "Subtitles error": "Error formatting subtitle information", "get video_info error": "Error getting video information, please check if the video can be played.", "nogptsovitsurl": "You must fill in the api address of GPT-SoVITS", "nogptsovitslanguage": "GPT-SoVITS only supports Chinese, English and Japanese languages", "miandailigoogle": "Use Google translate when no proxy", "ttsapi_nourl": "The url of the customized TTS-API must be filled in before it can be used", "import src error": "Error importing subtitles, please check if srt subtitle format content exists in the file", "openaimodelnot": "You choose the openai {name} model , but the {name} model does not exist, please download and put the pt file in the models folder in the software directory, click on the menubar - Help Support - Download Model  ", "recogn result is empty": "No subtitle was recognized, please check if it contains human voice and if the voice category matches the original {lang} language you chose, if both are normal, please select the 'Preserve background sound' option in the standard function mode and retry", "pianduan": " Partment ", "Download Models": "Download Models", "Start Separate": "Start separation", "Start Separate...": "Separating/click stop", "Separate End/Restart": "Separation completed/starting again", "zimusrterror": "The subtitle area already has subtitles that do not meet the SRT format requirements. Please delete all subtitle area text or import the correct SRT subtitle format again", "Export srt": "Export srt", "When subtitles exist, the subtitle content can be saved to a local SRT file": "When subtitles exist, the subtitle content can be saved to a local SRT file", "You must fill in the YouTube video playback page address": "You must fill in the YouTube video playback page address", "Error merging background and dubbing": "Error merging background and dubbing", "only10": "This software only supports Win10 and above systems on the Windows platform", "sp.exeerror": "sp.exe must be located in the original directory after decompression (i.e. in the same directory as videotrans|models|_internal), please do not copy or move it to any other location without permission", "meitiaozimugeshi": "Each subtitle format is as follows:\nLine number\nStart time hours:minutes:seconds,milliseconds -->End time hours:minutes:seconds,milliseconds\nSubtitle content", "Set up separate dubbing roles for each subtitle to be used": "Set up separate dubbing roles for each subtitle to be used", "daoruzimutips": "Existing SRT subtitle files can be imported locally, skipping the recognition and translation stages and using the imported subtitles directly", "Click to start the next step immediately": "Click to start the next step immediately", "Click to pause and modify subtitles for more accurate processing": "Click to pause and modify subtitles for more accurate processing", "zimubianjitishi": "During pause period, subtitles can be edited", "test google": "Test connection Google..", "Select Out Dir": "Select Out Dir", "downing...": "Downloading...", "start download": "Start Download", "Down done succeed": "Down done succeed", "videodown..": "Video is being slowed down", "Dubbing": "Dubbing..", "Separating background music": "Separating background music", "Unable to connect to the API": "Unable to connect to the API", "bixutianxiecloneapi": "You must be input api fill in the address in the menu bar-Settings-cloneVoice ", "Save": "Save", "Open Documents": "Open Documents Website", "Preserve the original sound in the video": "Preserve the original sound in the video", "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices": "Clone voice cannot be used in subtitle dubbing mode as there are no replicable voices", "lanjie": "Active restriction", "The ott project at": "The OTT text translate at: github.com/jianchang512/ott", "No select videos": "No select videos", "You must deploy and start the clone-voice service": "You must deploy and start the github.com/jianchang512/clone-voice  service, and fill in the address in the menu bar-Settings-cloneVoice", "cannot connection to clone-voice service": "Cannot connection to clone-voice service,You must deploy and start the github.com/jianchang512/clone-voice  service, and fill in the address in the menu bar-Settings-cloneVoice", "test clone voice": "Test connecting to clone-voice api ...", "The project at": "<PERSON><PERSON> voice at: github.com/jianchang512/clone-voice", "qianyiwenjian": "The video path or name contains non ASCII spaces. To avoid errors, it has been migrated to ", "Separating vocals and background music, which may take a longer time": "Separating vocals and background music, which may take a longer time", "mansuchucuo": "Video automatic slow error, please try to cancel the 'Video auto down' option", "zimuwenjianbuzhengque": "Subtitles file error,size is 0b", "huituicpu": "Execution error on GPU, rollback to CPU execution", "zimuhangshu": "subtitles Line ", "kaishihebing": "Start merging and outputting result files", "kaishishibie": "Start speech recognition", "kaishitiquyinpin": "Start extracting audio", "endfenliyinpin": "Separate end wait recognized", "kaishiyuchuli": "Start preprocessing video into standard format", "fengeyinpinshuju": "Split data before speech recognition", "yuyinshibiejindu": "Speech recognition progress", "yuyinshibiewancheng": "Speech recognition completed", "shipinmoweiyanchang": "Extend video end", "shipinjiangsu": "Extend video", "bukebaoliubeijing": "Must have a voice actor and input video to preserve background music", "xinshipinchangdu": "New video length after slowing down", "peiyin-yingzimu": "Synthesizing voice-over + hard subtitle", "peiyin-ruanzimu": "Synthesizing voice-over + soft subtitle", "onlypeiyin": "Embedding voice-over, no subtitles", "onlyyingzimu": "Embedding hard subtitles, no voice-over", "onlyruanzimu": "Embedding soft subtitles, no voice-over", "tuodonghuoshuru": "Enter text or drag the subtitle SRT file here", "ffmpegerror": "Please check if the CUDA configuration is correct or if the video is an H264 encoded mp4 file", "sjselectmp4": "Double click to select video or drag video here", "default": "default", "zhishaoxuanzeyihang": "At least one row must be selected", "continue_action": "Go next step", "xuanzejuese": "must to be select dubbing role, frist select subtitle language and select dubbing role", "tencent_key": "must be input tencent's Secret<PERSON>d and <PERSON><PERSON><PERSON>", "qingqueren": "Please confirm", "only_srt": "Not select target language，will only create srt file，click Yes to continue,else cancel", "mustberole": "must be selet role for listen", "setdeepl_authkey": "must be set DeepL token", "setdeeplx_address": "must be set DeepLX address and port", "setott_address": "must be set OTT address and port", "subtitle_tips": " Edit subtitle here or drap srt file to here ", "waitclear": "closing process", "whisper_type_all": "Overall", "whisper_type_split": "Pre-split", "whisper_type_avg": "Equal-division", "fenge_tips": "Overall: The model automatically breaks sentences for the whole audio.\nPre-split: suitable for very large videos, cut into 1-minute clips to recognize and break sentences one by one.\nEqual-division: cut equally according to a fixed number of seconds, each subtitle is of equal length.", "processingstatusbar": "Process video:[{var1}] total, [{var2}] waitting", "yinsekelong": "Timbre cloning will use github.com/jianchang512/clone-voice. These features will then be used as the voice for dubbing characters, achieving custom dubbing with any desired timbre.", "yinsekaifazhong": "Timbre cloning is under development.", "installffmpeg": "No ffmpeg, please go to ffmpeg.org to download and place the file ffmpeg and ffprobe under the root of this software", "hechengchucuo": "Composing video error，lost file:", "queding": "Confirm", "wait_edit_subtitle": "Wait for edit subtitle", "autocomposing": " seconds After the countdown, the video will be automatically synthesized. click Pause, the countdown will stop", "deepl_nosupport": "Don't support translation to the language", "deepl_authkey": "You need an DeepL authentication key", "confirmstop": "Stop this task?", "prcoessingstatusbar": "Processing video: [{var1}], with [{var2}] waiting to be processed", "createdirerror": "create dir error", "waitforend": "Composing video", "waitsubtitle": "Wait edit subtitle(click for continue)", "baikeymust": "input your baidu key", "chatgptkeymust": "input your chatgpt key", "nosubtitle": "No Subtitle", "embedsubtitle": "Embed subtitle", "softsubtitle": "Soft subtitle", "embedsubtitle2": "Embed subtitle(double)", "softsubtitle2": "Soft subtitle(double)", "modellost": "There is an error in the model download or the download is incomplete. Please re-download and store it in the models directory.", "modelpathis": "Model storage path:", "downloadmodel": "The {name} model does not exist. click on the menubar - Help Support - Download Model", "waitrole": "getting voice role list,hold on", "selectsavedir": "select an dir for output", "selectmp4": "select an mp4 video", "subtitleandvoice_role": "no video and has subtitle at edit area,will create dubbing audio wav files,confirm for continue?", "proxyerrortitle": "Proxy Error", "shoundselecttargetlanguage": "Must select a target language ", "proxyerrorbody": "Failed to access Google services. Please set up the proxy correctly.", "softname": "pyVideoTrans Translation and Dubbing", "anerror": "An error occurred", "selectvideodir": "You must select the video to be translated", "sourenotequaltarget": "Source language and target language must not be the same", "running": "Running...", "ing": "Running...", "exit": "Exit", "end": "Ended(click restart)", "stop": "Stop(click restart)", "error": "<PERSON>rro<PERSON>red(click restart)", "daoruzimu": "Import subtitles", "shitingpeiyin": "Trial dubbing", "xianqidongrenwu": "Firstly start the task, you can listen to it after the subtitle translation is completed, the dubbing speed and auto-acceleration take effect in real time", "juanzhu": " Please consider donating to the software to keep it updated and maintained! ", "nocuda": "Your device does not meet CUDA acceleration requirements, please confirm it is an NVIDIA graphics card and CUDA environment has been configured, click menubar-Help&support-CUDA help", "nocudnn": "Your device does not have cuDNN installed and configured, please refer to the https://juejin.cn/post/7318704408727519270#heading-1 Install and then restart the software,or use openai model", "cudatips": "Enable if you have an NVIDIA graphics card and configured with a CUDA environment, will greatly improve execution speed", "noselectrole": "No role selected, cannot trial listening", "chongtingzhong": "Re-listening", "shitingzhong": "Listening/Click to re-listen", "bukeshiting": "No subtitle content, cannot trial listening", "tiquzimu": "Set the original language to the language voiced in the video, the target language is the language you want to translate to", "kaishitiquhefanyi": "Start extracting and translating", "tiquzimuno": "Set the original language to the language voiced in the video", "kaishitiquzimu": "Start extracting subtitles", "endtiquzimu": "End of voice recognition waiting for the next step", "endtrans": "End of translation wait for next step", "duiqicaozuo": "Begin processing audio screen subtitle alignment", "zimu_video": "Choose the video to be merged, drag and drop the subtitles srt file to the right subtitle area", "zimu_peiyin": "Please set the target language as the language used for subtitles and select the dubbing role", "kaishipeiyin": "Start voiceover", "anzhuangvlc": "You may need to install the VLC decoder first,", "jixuzhong": "Continue executing", "nextstep": "Move on to the next step", "tianxieazure": "Must fill in Azure key", "bencijieshu": "This task is over", "kaishichuli": "Start processing", "yunxingbukerole": "Running, cannot change to without dubbing role", "bixutianxie": "Must fill in", "peiyinmoshisrt": "Must choose a dubbing role, target language in dubbing mode, and drag and drop the local srt subtitle file to the right subtitle area", "hebingmoshisrt": "In merge mode, you must select a video, subtitle embedding type, and drag and drop the subtitles srt file to the right subtitle area", "fanyimoshi1": "Must choose the target language to translate to", "bukedoubucunzai": "Videos and subtitles cannot be non-existent at the same time!", "wufapeiyin": "No target language selected, can't dubbing, please select target language or cancel dubbing role", "xuanzeyinpinwenjian": "Select audio and video files", "vlctips": "Drag the video here or double-click to select the video", "vlctips2": "file is not exists", "xuanzeyinshipin": "Click to select or drag and drop audio, video files here", "tuodongdaoci": "Drag the file to be converted here and release", "tuodongfanyi": "import subtitles  srt file", "zhixingwc": "Execution completed", "zhixinger": "Execution error", "starttrans": "Start translating", "yinpinhezimu": "At least one of audio and subtitles must be selected", "bixuyinshipin": "Must select a valid audio and video file", "chakanerror": "Pre-processing failed before recognition, please confirm if there is audio data in the video", "srtisempty": "The subtitle content is empt", "savesrtto": "Choose to save the subtitle file to..", "neirongweikong": "Content cannot be empty", "yuyanjuesebixuan": "Language and role must be selected", "nojueselist": "Did not get the role list", "buzhichijuese": "This voice role is not supported", "nowenjian": "No valid files exist", "yinpinbuke": "Audio cannot be converted to", "quanbuend": "All conversions are done", "wenbenbukeweikong": "The text to be translated cannot be empty", "buzhichifanyi": "Does not support translation to the target language", "ffmpegno": "ffmpeg not found, the software is not available, please download from ffmpeg.org and add to system environment variables", "newversion": "There is a new version to download", "tingzhile": "Stopped", "geshihuazimuchucuo": "Formatting subtitle file error", "moweiyanchangshibai": "Failed to add extension video frames at the end, will maintain the original state without extending the video", "xiugaiyuanyuyan": "Wait to modify original language subtitles / continue", "jimiaohoufanyi": "Automatically translated in seconds, you can click Pause and edit subtitles so that the translation is more accurate", "xiugaipeiyinzimu": "Wait to modify the dubbed subtitles / click continue", "zidonghebingmiaohou": "Automatically merge in seconds, you can click <PERSON><PERSON> to modify the subtitles so that the dubbing is more accurate", "jieshutips": "Video processing ends: Relevant materials can be found in the target folder, including subtitle files, voiceover files, etc", "mubiao": "Open output folder", "endandopen": "Ended click to open ", "waitforstart": "Wait for starting", "xianxuanjuese": "Please select TTS type and role first", "shezhijueseline": "Fill in the number of lines using the character's voice acting, eg. 2,3,7,8", "youzimuyouset": "Multiple roles can only be set by line after displaying complete subtitles in the subtitle area", "shezhijuese": "Set Role"}, "ui_lang": {"action_xinshoujandan": "Minimalist Newbie Mode", "action_xinshoujandan_tools": "Simple to use without configuration, suitable for short videos, less accurate, for more control use standard function mode", "onlyvideo": "Save only video", "onlyvideo_tips": "If this item is checked, subtitles, audio and other material will not be kept, only the final video file will be saved", "model_type_tips": "The master model is faster and more resource-efficient, but requires the installation and configuration of cudnn and cublas in addition to cuda.\nThe openai model is slower and more resource-intensive, but only requires cuda to be installed.", "faster model": "faster-whisper", "openai model": "openai-whisper", "addbackbtn": "Add background music", "back_audio_place": "The full path of the background music, deleting it does not add it", "Download Models": "Download Models", "Download cuBLASxx.dll": "Download cuBLASxx.dll", "Separate vocal voice": "BackgroundVocalSeparate", "SP-video Translate Dubbing": "VideoTrans - Video Translate Dubbing", "Multiple MP4 videos can be selected and automatically queued for processing": "Multiple MP4 videos can be selected and automatically queued for processing", "Select video..": "Select video..", "Select where to save the processed output resources": "Select where to save the processed output resources", "Save to..": "Save to..", "Open target dir": "Open target dir", "Open": "Open", "Translate channel": "Translate channel", "Proxy": "Proxy", "proxy address": "proxy address", "shuoming01": "Click to listen to the pronunciation of the current dubbing character,Generating dubbing may take a few seconds, please be patient and wait", "Trial dubbing": "Trial dubbing", "Source lang": "Source lang", "The language used for the original video pronunciation": "The language used for the original video pronunciation", "Target lang": "Target lang", "What language do you want to translate into": "What language do you want to translate into", "Dubbing role": "Dubbing role", "No is not dubbing": "No is not dubbing", "From base to large v3, the effect is getting better and better, but the speed is also getting slower and slower": "From base to large v3, the effect is getting better and better, but the speed is also getting slower and slower", "Overall recognition is suitable for videos with or without background music and noticeable silence": "Overall recognition is suitable for videos with or without background music and noticeable silence", "Embed subtitles": "Embed subtitles", "shuoming02": "Embedded subtitles always display subtitles no matter where they are played and cannot be hidden.If supported by the player, soft subtitles can be controlled to be displayed or hidden in the player.\n,If you want to display subtitles when playing on a webpage, please select embedded subtitles.", "Silent duration": "Silent duration", "default 500ms": "default 500ms", "Mute duration for segmented speech, in milliseconds": "Mute duration for segmented speech, in milliseconds", "Dubbing speed": "Dubbing speed", "Overall acceleration or deceleration of voice over playback": "Overall acceleration or deceleration of voice over playback", "Positive numbers accelerate, negative numbers decelerate, -90 to+90": "Positive numbers accelerate, negative numbers decelerate, -90 to+90", "shuoming03": "After the translation of different languages under the pronunciation of different lengths, there is bound to be alignment problems, through the dubbing of the overall speed of speech, the dubbing of the automatic acceleration, the voice before and after the extension can be slightly alleviated, more methods and principles, please check the lower left corner of the relevant tutorials", "Voice acceleration?": "Voice acceleration", "shuoming04": "The duration of pronunciation varies in different languages after translation, for example, if a sentence is in Chinese for 3 seconds, it may take 5 seconds to translate it into English, resulting in inconsistency between the duration and the video.\nTwo solutions:\n1. Force voice over to accelerate playback, in order to shorten the duration of voice over and align with the video\n2. Force the video to play slowly in order to extend the video duration and align the voice over.\nChoose only one of the two options", "Video slow": "Video slow", "shuoming05": "It is necessary to ensure that there is an NVIDIA graphics card and that the CUDA environment is correctly configured, therwise do not choose", "Enable CUDA?": "Enable CUDA", "Preserve background music": "Preserve BackMusic", "If retained, the required time may be longer, please be patient and wait": "If retained, the required time may be longer, please be patient and wait", "Start": "Start", "Pause": "Pause", "Import srt": "Import srt", "Train voice": "Train voice", "Set role by line": "Set role by line", "&Setting": "&TransText Set", "&TTSsetting": "TTS Set(&E)", "&RECOGNsetting": "Speech to Text(&R)", "&Tools": "&Tools/ADVSet", "&Help": "&Help", "toolBar": "toolBar", "Video Toolbox": "Video Toolbox", "Go VLC Website": "Go VLC Website", "FFmpeg": "FFmpeg", "Go FFmpeg website": "Go FFmpeg website", "Post issue": "GitHub Issues", "Clone Voice": "<PERSON><PERSON> Voice", "Documents": "Documents", "Donating developers": "Donating developers", "Standard Function Mode": "Standard Function Mode", "Display all options for video translation and dubbing": "Display all options for video translation and dubbing", "Export  Srt  From Videos": "Export  Srt  From Videos", "Extracting SRT subtitles in the original language from local videos": "Extracting SRT subtitles in the original language from local videos", "Merging Subtitle  Video": "Merging Subtitle  Video", "Embed locally existing SRT subtitles into the video": "Embed locally existing SRT subtitles into the video", "Subtitle Create Dubbing": "Subtitle Create Dubbing", "Local existing SRT subtitle generation dubbing WAV files": "Local existing SRT subtitle generation dubbing WAV files", "Speech Recognition Text": "Speech Recognition Text", "Recognize the sound in audio or video and output SRT text": "Recognize the sound in audio or video and output SRT text", "From  Text  Into  Speech": "From  Text  Into  Speech", "Generate audio WAV from text or SRT subtitle files": "Generate audio WAV from text or SRT subtitle files", "Extract Srt And Translate": "Extract Srt And Translate", "Extract SRT subtitles from local videos in the original language and translate them into SRT subtitle files in the target language": "Extract SRT subtitles from local videos in the original language and translate them into SRT subtitle files in the target language", "Separate Video to audio": "Separate Video to audio", "Separate audio and silent videos from videos": "Separate audio and silent videos from videos", "Video Subtitles Merging": "Video Audio Srt Merging", "Merge audio, video, and subtitles into one file": "Merge audio, video, and subtitles into one file", "Files Format Conversion": "Files Format Conversion", "Convert various formats to each other": "Convert various formats to each other", "Mixing 2 Audio Streams": "Mixing 2 Audio Streams", "Mix two audio files into one audio file": "Mix two audio files into one audio file", "Text  Or Srt  Translation": "Batch translation subtitles", "Translate text or subtitles": "Translate text or subtitles", "Download from Youtube": "Download from Youtube", "Whisper model": "Whisper model"}, "toolbox_lang": {"import audio or video": "Import audio or video", "Video Toolbox": "Video Toolbox", "Start": "Start", "No voice video": "No voice video", "Open dir": "Open dir", "Audio Wav": "Audio Wav", "Video audio separation": "Video audio separation", "Video file": "Video file", "Select video": "Select video", "Audio file": "Audio file", "Select audio": "Select audio", "Subtitle srt": "Subtitle srt", "Select srt file": "Select srt file", "Open output dir": "Open output dir", "Video subtitle merging": "Video subtitle merging", "Source lang": "Source lang", "Whisper model": "Whisper model", "Save to srt..": "Save to srt..", "Voice recognition": "Voice recognition", "Subtitle lang": "Subtitle lang", "Select role": "Select role", "Speed change": "Speed change", "Negative deceleration, positive acceleration": "Negative deceleration, positive acceleration", "If so, the line number and time value will skip reading aloud": "If so, the line number and time value will skip reading aloud", "Is srt?": "Is srt?", "Automatic acceleration?": "Automatic acceleration?", "Output audio name": "Output audio name", "Set the name of the generated audio file here. If not filled in, use the time and date command": "Set the name of the generated audio file here. If not filled in, use the time and date command", "Text to speech": "Text to speech", "Convert mp4->": "Convert mp4->", "Convert avi->": "Convert avi->", "Convert mov->": "Convert mov->", "Convert wav->": "Convert wav->", "Convert mp3->": "Convert mp3->", "Convert aac->": "Convert aac->", "Convert m4a->": "Convert m4a->", "Conver flac->": "Conver flac->", "The conversion result is displayed here": "The conversion result is displayed here", "Audio and video format conversion": "Audio and video format conversion", "Audio file 1": "Audio file 1", "Select the first audio file": "Select the first audio file", "Audio file 2": "Audio file 2", "Select the second audio file": "Select the second audio file", "You can customize the output file name here. If not filled in, use a date name": "You can customize the output file name here. If not filled in, use a date name", "Mixing two audio streams": "Mixing two audio streams", "Translation channels": "Translation channels", "Target lang": "Target lang", "Proxy": "Proxy", "Failed to access Google services. Please set up the proxy correctly": "set up the proxy", "Import text to be translated from a file..": "Import text to be translated from a file..", "shuoming1": "Only srt format subtitle files are allowed to be translated, please do not import and translate files that do not symbolize this format, otherwise an error will be reported.", "export..": "export..", "Start>": "Start>", "The translation result is displayed here": "The translation result is displayed here", "Text subtitle translation": "Text subtitle translation"}, "language_code_list": {"zh-cn": "Simplified Chinese", "zh-tw": "Traditional Chinese", "en": "English", "fr": "French", "de": "German", "ja": "Japanese", "ko": "Korean", "ru": "Russian", "es": "Spanish", "th": "Thai", "it": "Italian", "pt": "Portuguese", "vi": "Vietnamese", "ar": "Arabic", "tr": "Turkish", "hi": "Hindi", "hu": "Hungarian", "uk": "Ukrainian", "id": "Indonesian", "ms": "Malay", "kk": "Kazakh", "cs": "Czech", "pl": "Polish", "nl": "Dutch", "sv": "Swedish", "he": "Hebrew", "bn": "Bengali", "fil": "Filipino", "fi": "Finnish", "fa": "Persian", "auto": "Detection"}}