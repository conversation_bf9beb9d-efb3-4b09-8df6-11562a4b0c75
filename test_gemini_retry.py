#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for improved Gemini translator with retry logic
测试改进后的Gemini翻译器重试逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from videotrans.translator._gemini import Gemini
from videotrans.configure import config

def test_gemini_retry():
    """
    Test the improved Gemini translator with retry logic
    """
    print("🧪 Testing Gemini Translator with Retry Logic")
    print("=" * 50)
    
    # Sample subtitle data
    test_subtitles = [
        {"line": 1, "time": "00:00:01,000 --> 00:00:03,000", "text": "Hello world"},
        {"line": 2, "time": "00:00:03,000 --> 00:00:05,000", "text": "How are you today?"},
        {"line": 3, "time": "00:00:05,000 --> 00:00:07,000", "text": "This is a test subtitle"},
    ]
    
    # Initialize Gemini translator
    try:
        translator = <PERSON>(
            text_list=test_subtitles,
            target_language_name="Vietnamese",
            target_code="vi",
            source_code="en",
            is_srt=True,
            is_test=True
        )
        
        print(f"✅ Gemini translator initialized successfully")
        print(f"📝 Target language: {translator.target_language_name}")
        print(f"🔑 API keys configured: {len(translator.api_keys)}")
        
        # Test individual translation
        test_text = "Hello world\nHow are you?"
        print(f"\n🔄 Testing translation of: '{test_text}'")
        
        result = translator._item_task(test_text)
        print(f"✅ Translation result: '{result}'")
        
        # Test clean raw response function
        raw_response = "Here is the translation: Xin chào thế giới\nBạn có khỏe không?"
        cleaned = translator._clean_raw_response(raw_response, test_text)
        print(f"\n🧹 Testing clean raw response:")
        print(f"   Raw: '{raw_response}'")
        print(f"   Cleaned: '{cleaned}'")
        
        # Test translation quality validation
        translation = "Xin chào thế giới"
        original = "Hello world"
        is_valid = translator._validate_translation_quality(translation, original)
        print(f"\n✅ Translation quality validation: {is_valid}")
        
        print(f"\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gemini_retry()
