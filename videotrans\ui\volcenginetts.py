# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_volcengineform(object):
    def setupUi(self, volcengineform):
        self.has_done = False
        volcengineform.setObjectName("volcengineform")
        volcengineform.setWindowModality(QtCore.Qt.NonModal)
        volcengineform.resize(400, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(volcengineform.sizePolicy().hasHeightForWidth())
        volcengineform.setSizePolicy(sizePolicy)
        volcengineform.setMaximumSize(QtCore.QSize(400, 300))

        self.verticalLayout = QtWidgets.QVBoxLayout(volcengineform)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.volcenginetts_appid = QtWidgets.QLineEdit(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.volcenginetts_appid.sizePolicy().hasHeightForWidth())
        self.volcenginetts_appid.setSizePolicy(sizePolicy)
        self.volcenginetts_appid.setMinimumSize(QtCore.QSize(210, 35))
        self.volcenginetts_appid.setObjectName("volcenginetts_appid")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.volcenginetts_appid)
        self.verticalLayout.addLayout(self.formLayout_2)



        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout.setObjectName("formLayout")
        self.label_2 = QtWidgets.QLabel(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(100, 35))
        self.label_2.setSizeIncrement(QtCore.QSize(0, 35))
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_2)

        self.volcenginetts_access = QtWidgets.QLineEdit(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.volcenginetts_access.sizePolicy().hasHeightForWidth())
        self.volcenginetts_access.setSizePolicy(sizePolicy)
        self.volcenginetts_access.setMinimumSize(QtCore.QSize(210, 35))
        self.volcenginetts_access.setObjectName("volcenginetts_access")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.volcenginetts_access)
        self.verticalLayout.addLayout(self.formLayout)

        self.formLayout3 = QtWidgets.QFormLayout()
        self.formLayout3.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout3.setFormAlignment(QtCore.Qt.AlignLeading | QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter)
        self.formLayout3.setObjectName("formLayout")
        self.label_cluster = QtWidgets.QLabel(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_cluster.sizePolicy().hasHeightForWidth())
        self.label_cluster.setSizePolicy(sizePolicy)
        self.label_cluster.setMinimumSize(QtCore.QSize(100, 35))
        self.label_cluster.setSizeIncrement(QtCore.QSize(0, 35))
        self.label_cluster.setObjectName("label_cluster")
        self.formLayout3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_cluster)

        self.volcenginetts_cluster = QtWidgets.QLineEdit(volcengineform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.volcenginetts_cluster.sizePolicy().hasHeightForWidth())
        self.volcenginetts_cluster.setSizePolicy(sizePolicy)
        self.volcenginetts_cluster.setMinimumSize(QtCore.QSize(210, 35))
        self.volcenginetts_cluster.setObjectName("volcenginetts_cluster")
        self.formLayout3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.volcenginetts_cluster)
        self.verticalLayout.addLayout(self.formLayout3)

        h4=QtWidgets.QHBoxLayout()

        self.set = QtWidgets.QPushButton(volcengineform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(volcengineform)
        self.test.setMinimumSize(QtCore.QSize(0, 35))
        self.test.setObjectName("test")


        help_btn = QtWidgets.QPushButton(volcengineform)
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda :tools.open_url(url='https://pyvideotrans.com/volcenginetts'))

        h4.addWidget(self.set)
        h4.addWidget(self.test)
        h4.addWidget(help_btn)
        self.verticalLayout.addLayout(h4)


        self.retranslateUi(volcengineform)
        QtCore.QMetaObject.connectSlotsByName(volcengineform)

    def retranslateUi(self, volcengineform):
        volcengineform.setWindowTitle("字节火山语音合成设置" if config.defaulelang=='zh' else "Volcengine TTS Setting")
        self.label.setText("App id")
        self.label_2.setText("Access token")
        self.label_cluster.setText("Cluster id")
        self.set.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试' if config.defaulelang == 'zh' else "Test")
