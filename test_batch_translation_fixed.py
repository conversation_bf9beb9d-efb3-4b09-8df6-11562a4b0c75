#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for batch translation with fixed error handling
"""

import sys
import os
import time
from pathlib import Path

# Add current directory to Python path
current_dir = Path.cwd()
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

def create_test_subtitle():
    """Create a test subtitle file"""
    print("📝 Creating test subtitle file...")
    
    try:
        from videotrans.configure import config
        
        # Create test content
        test_content = """1
00:00:01,000 --> 00:00:03,000
Hello world

2
00:00:03,000 --> 00:00:05,000
How are you today?

3
00:00:05,000 --> 00:00:07,000
This is a test subtitle
"""
        
        # Create test directory
        test_dir = Path(config.TEMP_HOME) / "batch_test_fixed"
        test_dir.mkdir(parents=True, exist_ok=True)
        
        # Create test file
        test_file = test_dir / "test_subtitle.srt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✅ Created test file: {test_file}")
        return str(test_file)
        
    except Exception as e:
        print(f"❌ Error creating test file: {e}")
        return None

def test_gemini_translator_directly():
    """Test Gemini translator directly"""
    print("\n🧪 Testing Gemini translator directly...")
    
    try:
        from videotrans.configure import config
        from videotrans.translator._gemini import Gemini
        
        # Check API key
        api_key = config.params.get('gemini_key', '').strip()
        if not api_key:
            print("❌ No Gemini API key configured")
            return False
        
        print(f"✅ API key configured: {api_key[:8]}...")
        
        # Create translator instance
        test_texts = ["Hello world", "How are you?"]
        
        translator = Gemini(
            text_list=test_texts,
            target_language_name="Vietnamese",
            target_code="vi",
            source_code="en",
            is_srt=True
        )
        
        print("✅ Translator instance created")
        
        # Test translation
        for i, text in enumerate(test_texts):
            print(f"\n  Testing text {i+1}: '{text}'")
            try:
                result = translator._item_task(text)
                print(f"  ✅ Result: '{result}'")
            except Exception as e:
                print(f"  ❌ Translation failed: {e}")
                if "candidates is empty" in str(e):
                    print("  💡 This is the 'candidates empty' error we're fixing")
                return False
        
        print("✅ Direct translator test passed")
        return True
        
    except Exception as e:
        print(f"❌ Direct translator test failed: {e}")
        return False

def test_batch_translation_workflow():
    """Test the complete batch translation workflow"""
    print("\n🔄 Testing batch translation workflow...")
    
    try:
        from videotrans.configure import config
        from videotrans.task._translate_srt import TranslateSrt
        from videotrans.util import tools
        from videotrans import translator
        
        # Create test file
        test_file = create_test_subtitle()
        if not test_file:
            return False
        
        # Parse subtitle
        subtitle_list = tools.get_subtitle_from_srt(test_file)
        print(f"✅ Parsed {len(subtitle_list)} subtitle entries")
        
        # Format video info
        it = tools.format_video(test_file, None)
        print(f"✅ Video info formatted: {it['uuid']}")
        
        # Get language codes
        source_code = translator.get_code(show_text="English")
        target_code = translator.get_code(show_text="Vietnamese")
        
        print(f"✅ Language codes: {source_code} -> {target_code}")
        
        # Check if translation is allowed
        gemini_index = 5  # Assuming Gemini is at index 5
        rs = translator.is_allow_translate(translate_type=gemini_index, show_target="Vietnamese")
        
        if rs is not True:
            print(f"❌ Translation not allowed: {rs}")
            return False
        
        print("✅ Translation is allowed")
        
        # Create translation task
        result_dir = Path(config.TEMP_HOME) / "batch_test_fixed" / "results"
        result_dir.mkdir(parents=True, exist_ok=True)
        
        trk = TranslateSrt({
            "out_format": 0,
            "translate_type": gemini_index,
            "text_list": subtitle_list,
            "target_dir": str(result_dir),
            "inst": None,
            "rename": False,
            "uuid": it['uuid'],
            "source_code": source_code,
            "target_code": target_code
        }, it)
        
        print("✅ Translation task created")
        
        # Set translation status
        config.box_trans = 'ing'
        
        # Run translation
        print("\n🔄 Running translation...")
        try:
            trk.prepare()
            trk.trans()
            trk.task_done()
            
            # Check result
            target_file = trk.cfg.get('target_sub')
            if target_file and Path(target_file).exists():
                print(f"✅ Translation completed successfully")
                print(f"   Result file: {target_file}")
                
                # Read and display result
                with open(target_file, 'r', encoding='utf-8') as f:
                    result_content = f.read()
                
                print(f"   Content preview:")
                lines = result_content.split('\n')[:10]  # First 10 lines
                for line in lines:
                    if line.strip():
                        print(f"     {line}")
                
                return True
            else:
                print("❌ Translation completed but no result file found")
                return False
                
        except Exception as e:
            print(f"❌ Translation failed: {e}")
            if "candidates is empty" in str(e):
                print("💡 This is the 'candidates empty' error")
                print("🔧 The fix should handle this automatically with retries")
            return False
        
    except Exception as e:
        print(f"❌ Batch translation workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_recovery():
    """Test error recovery mechanisms"""
    print("\n🛡️ Testing error recovery mechanisms...")
    
    try:
        from videotrans.configure import config
        from videotrans.translator._gemini import Gemini
        
        # Test with potentially problematic content
        problematic_texts = [
            "Test content that might trigger safety filters",
            "Normal content that should work fine",
            "Another test to verify retry logic"
        ]
        
        translator = Gemini(
            text_list=problematic_texts,
            target_language_name="Vietnamese",
            target_code="vi",
            source_code="en",
            is_srt=True
        )
        
        success_count = 0
        
        for i, text in enumerate(problematic_texts):
            print(f"\n  Testing recovery for text {i+1}: '{text[:30]}...'")
            try:
                result = translator._item_task(text)
                print(f"  ✅ Success: '{result[:50]}...'")
                success_count += 1
            except Exception as e:
                print(f"  ⚠️  Failed but handled: {str(e)[:100]}...")
                # This is expected for some content
        
        if success_count > 0:
            print(f"✅ Error recovery test passed ({success_count}/{len(problematic_texts)} succeeded)")
            return True
        else:
            print("❌ All texts failed - may indicate API issues")
            return False
        
    except Exception as e:
        print(f"❌ Error recovery test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Batch Translation Fixed - Test Suite")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path('videotrans').exists():
        print("❌ Please run this script from the PyVideoTrans root directory")
        return False
    
    # Run tests
    tests = [
        ("Gemini Translator Direct", test_gemini_translator_directly),
        ("Error Recovery", test_error_recovery),
        ("Batch Translation Workflow", test_batch_translation_workflow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("="*60)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Batch translation should work now.")
        print("\n💡 The 'response.candidates is empty' error has been fixed with:")
        print("1. ✅ Enhanced error detection and handling")
        print("2. ✅ Automatic retry logic with modified prompts")
        print("3. ✅ Safety settings optimization")
        print("4. ✅ Graceful fallback mechanisms")
        
        print("\n🚀 You can now use batch translation:")
        print("1. Open Tools > Subtitle Editor")
        print("2. Import your subtitle file")
        print("3. Select Gemini as translation service")
        print("4. Choose source and target languages")
        print("5. Click 'Running...' to start translation")
        
    elif passed > 0:
        print(f"\n⚠️  {passed}/{total} tests passed. Some issues remain.")
        print("💡 Try running: python fix_gemini_candidates_error.py")
        
    else:
        print("\n❌ All tests failed. Please check:")
        print("1. Gemini API key configuration")
        print("2. Internet connection")
        print("3. API quota and permissions")
        print("4. Run: python fix_gemini_candidates_error.py")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
