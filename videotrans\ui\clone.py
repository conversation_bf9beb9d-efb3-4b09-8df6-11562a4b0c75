# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QLabel

from videotrans.configure import config
from videotrans.util import tools


class Ui_cloneform(object):
    def setupUi(self, clone):
        self.has_done = False
        clone.setObjectName("clone")
        clone.setWindowModality(QtCore.Qt.NonModal)
        clone.resize(500, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(clone.sizePolicy().hasHeightForWidth())
        clone.setSizePolicy(sizePolicy)
        clone.setMaximumSize(QtCore.QSize(500, 300))


        self.verticalLayout = QtWidgets.QVBoxLayout(clone)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(clone)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.clone_address = QtWidgets.QLineEdit(clone)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.clone_address.sizePolicy().hasHeightForWidth())
        self.clone_address.setSizePolicy(sizePolicy)
        self.clone_address.setMinimumSize(QtCore.QSize(400, 35))
        self.clone_address.setObjectName("clone_address")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.clone_address)
        self.verticalLayout.addLayout(self.formLayout_2)


        self.set_clone = QtWidgets.QPushButton(clone)
        self.set_clone.setMinimumSize(QtCore.QSize(0, 35))
        self.set_clone.setObjectName("set_clone")

        self.test = QtWidgets.QPushButton(clone)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")
        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/clonevoice'))

        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")

        self.layout_btn.addWidget(self.set_clone)
        self.layout_btn.addWidget(self.test)
        self.layout_btn.addWidget(help_btn)

        self.verticalLayout.addLayout(self.layout_btn)



        self.retranslateUi(clone)
        QtCore.QMetaObject.connectSlotsByName(clone)

    def retranslateUi(self, clone):
        clone.setWindowTitle("clone-voice")
        self.label.setText("http地址" if config.defaulelang == 'zh' else 'clone-voice url')
        self.clone_address.setPlaceholderText(
            '填写clone-voice项目启动后的http地址' if config.defaulelang == 'zh' else 'Fill in the HTTP address after the clone voice program starts')
        self.set_clone.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试并更新角色' if config.defaulelang == 'zh' else "Test & update role")
