# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt

from videotrans.configure import config
from videotrans.util import tools


class Ui_elevenlabsform(object):
    def setupUi(self, elevenlabsform):
        self.has_done = False
        elevenlabsform.setObjectName("elevenlabsform")
        elevenlabsform.setWindowModality(QtCore.Qt.NonModal)
        elevenlabsform.resize(400, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(elevenlabsform.sizePolicy().hasHeightForWidth())
        elevenlabsform.setSizePolicy(sizePolicy)
        elevenlabsform.setMaximumSize(QtCore.QSize(400, 300))

        self.verticalLayout = QtWidgets.QVBoxLayout(elevenlabsform)
        self.verticalLayout.setObjectName("verticalLayout")
        
        
        self.formLayout_2 = QtWidgets.QHBoxLayout(elevenlabsform)
        self.formLayout_3 = QtWidgets.QHBoxLayout(elevenlabsform)
        
        
        self.label = QtWidgets.QLabel(elevenlabsform)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setObjectName("label")

        self.elevenlabstts_key = QtWidgets.QLineEdit(elevenlabsform)
        self.elevenlabstts_key.setMinimumSize(QtCore.QSize(210, 35))
        self.elevenlabstts_key.setObjectName("elevenlabstts_key")
        
        self.formLayout_2.addWidget(self.label)
        self.formLayout_2.addWidget(self.elevenlabstts_key)
        
        self.label2 = QtWidgets.QLabel(elevenlabsform)
        self.label2.setMinimumSize(QtCore.QSize(100, 35))
        self.label2.setObjectName("label2")

        self.elevenlabstts_models = QtWidgets.QComboBox(elevenlabsform)
        self.elevenlabstts_models.setMinimumSize(QtCore.QSize(210, 35))
        self.elevenlabstts_models.setObjectName("elevenlabstts_models")
        self.elevenlabstts_models.addItems(['eleven_flash_v2_5','eleven_multilingual_v2'])
        
        self.formLayout_3.addWidget(self.label2)
        self.formLayout_3.addWidget(self.elevenlabstts_models)
        
        self.verticalLayout.addLayout(self.formLayout_2)
        self.verticalLayout.addLayout(self.formLayout_3)



        self.set = QtWidgets.QPushButton(elevenlabsform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(elevenlabsform)
        self.test.setMinimumSize(QtCore.QSize(0, 35))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/elevenlabstts'))

        hv=QtWidgets.QHBoxLayout()
        hv.addWidget(self.set)
        hv.addWidget(self.test)
        hv.addWidget(help_btn)

        self.verticalLayout.addLayout(hv)

        self.retranslateUi(elevenlabsform)
        QtCore.QMetaObject.connectSlotsByName(elevenlabsform)

    def retranslateUi(self, elevenlabsform):
        elevenlabsform.setWindowTitle("ElevenLabs.io")
        self.label2.setText("TTS model")
        self.label.setText("API_KEY")
        self.set.setText('保存' if config.defaulelang == 'zh' else "Save")
        self.test.setText('测试并获取角色' if config.defaulelang == 'zh' else "Test & get roles")
