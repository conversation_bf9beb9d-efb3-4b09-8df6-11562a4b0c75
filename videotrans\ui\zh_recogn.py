# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QLabel

from videotrans.util import tools


class Ui_zhrecognform(object):
    def setupUi(self, zhrecogn):
        self.has_done = False
        zhrecogn.setObjectName("zhrecogn")
        zhrecogn.setWindowModality(QtCore.Qt.NonModal)
        zhrecogn.resize(500, 223)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(zhrecogn.sizePolicy().hasHeightForWidth())
        zhrecogn.setSizePolicy(sizePolicy)
        zhrecogn.setMaximumSize(QtCore.QSize(500, 300))

        self.verticalLayout = QtWidgets.QVBoxLayout(zhrecogn)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(zhrecogn)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.zhrecogn_address = QtWidgets.QLineEdit(zhrecogn)
        self.zhrecogn_address.setMinimumSize(QtCore.QSize(0, 35))
        self.zhrecogn_address.setObjectName("zhrecogn_address")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.zhrecogn_address)
        self.verticalLayout.addLayout(self.formLayout_2)


        self.set = QtWidgets.QPushButton(zhrecogn)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(zhrecogn)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程")
        help_btn.clicked.connect(lambda :tools.open_url(url='https://pyvideotrans.com/zh_recogn'))



        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")

        self.layout_btn.addWidget(self.set)
        self.layout_btn.addWidget(self.test)
        self.layout_btn.addWidget(help_btn)

        self.verticalLayout.addLayout(self.layout_btn)


        self.retranslateUi(zhrecogn)
        QtCore.QMetaObject.connectSlotsByName(zhrecogn)

    def retranslateUi(self, zhrecogn):
        zhrecogn.setWindowTitle("zh_recogn中文语音识别")
        self.label.setText("http接口地址")
        self.zhrecogn_address.setPlaceholderText('填写zh_recogn项目启动后的http接口地址')
        self.set.setText('保存')
        self.test.setText('测试')
