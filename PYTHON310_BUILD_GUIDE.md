# 🐍 PyVideoTrans Python 3.10 Build Guide

Hướng dẫn chi tiết để build PyVideoTrans thành file exe bằng Python 3.10.

## 🎯 Tại sao chọn Python 3.10?

- ✅ **Performance tốt hơn**: Faster startup, better memory usage
- ✅ **Compatibility**: Tương thích tốt với PyInstaller và PySide6
- ✅ **Stability**: Stable release với long-term support
- ✅ **Modern features**: Pattern matching, better error messages
- ✅ **AI/ML support**: Tương thích tốt với PyTorch, TensorFlow

## 🛠️ Chuẩn bị môi trường

### 1. Cài đặt Python 3.10

```bash
# Download từ python.org
https://www.python.org/downloads/release/python-31011/

# Hoặc sử dụng chocolatey (Windows)
choco install python310

# Hoặc sử dụng winget
winget install Python.Python.3.10
```

**⚠️ L<PERSON>u ý quan trọng:**
- ✅ Chọn "Add Python to PATH"
- ✅ Chọn "Install for all users"
- ✅ Chọn "Install pip"

### 2. Ki<PERSON>m tra cài đặt

```bash
# Kiểm tra Python 3.10
py -3.10 --version
# Output: Python 3.10.11

# Kiểm tra pip
py -3.10 -m pip --version
```

## 🚀 Build Process

### Option 1: Build tự động (Khuyến nghị)

```bash
# 1. Mở Command Prompt as Administrator
# 2. Navigate đến thư mục PyVideoTrans
cd C:\path\to\pyvideotrans

# 3. Chạy script tự động
build_setup.bat
```

### Option 2: Build thủ công

#### Bước 1: Setup virtual environment
```bash
# Tạo virtual environment
py -3.10 -m venv venv_py310

# Activate
venv_py310\Scripts\activate.bat

# Upgrade pip
python -m pip install --upgrade pip
```

#### Bước 2: Cài đặt dependencies
```bash
# Cài đặt requirements
pip install -r requirements.txt

# Cài đặt build tools
pip install pyinstaller>=5.13.0 setuptools>=65.0.0 wheel>=0.38.0
```

#### Bước 3: Build executable
```bash
# Build với script tối ưu Python 3.10
python build_exe_python310.py
```

## 📁 Kết quả build

```
dist/
└── PyVideoTrans/
    ├── PyVideoTrans.exe          # 🎯 Main executable
    ├── _internal/                # Dependencies
    ├── videotrans/              # Resources
    ├── VERSION_INFO.txt         # Build information
    ├── QUICK_START.txt          # User guide
    └── run.bat                  # Launcher
```

## 🔧 Tối ưu hóa

### Giảm kích thước file

1. **Exclude unused modules:**
```python
excludes=[
    'tkinter', 'matplotlib', 'scipy', 'pandas',
    'jupyter', 'notebook', 'IPython'
]
```

2. **Use UPX compression:**
```bash
# Cài đặt UPX
choco install upx
# Hoặc download từ https://upx.github.io/

# Build với UPX
upx=True  # trong spec file
```

3. **Remove debug info:**
```python
strip=True  # trong spec file
```

### Tăng tốc độ build

1. **Use SSD storage**
2. **Increase available RAM**
3. **Close unnecessary programs**
4. **Use parallel processing:**
```bash
python -m PyInstaller --clean --noconfirm pyvideotrans_py310.spec
```

## 🐛 Troubleshooting

### Lỗi thường gặp

#### 1. "Python 3.10 not found"
```bash
# Giải pháp:
# 1. Reinstall Python 3.10 với "Add to PATH"
# 2. Restart Command Prompt
# 3. Test: py -3.10 --version
```

#### 2. "ModuleNotFoundError"
```bash
# Giải pháp:
pip install --upgrade -r requirements.txt
pip list  # Kiểm tra installed packages
```

#### 3. "Permission denied"
```bash
# Giải pháp:
# 1. Run as Administrator
# 2. Disable antivirus temporarily
# 3. Add build folder to antivirus exclusions
```

#### 4. "Build failed with PyInstaller"
```bash
# Debug:
python -m PyInstaller --log-level=DEBUG pyvideotrans_py310.spec

# Check logs:
# - build/PyVideoTrans/warn-PyVideoTrans.txt
# - build/PyVideoTrans/xref-PyVideoTrans.html
```

#### 5. "Executable crashes on startup"
```bash
# Debug:
# 1. Run from command line để xem error
PyVideoTrans.exe

# 2. Check dependencies
python -c "import videotrans; print('OK')"

# 3. Test imports
python -c "import PySide6; print('PySide6 OK')"
```

### Performance Issues

#### Build quá chậm:
- ✅ Use SSD
- ✅ Increase RAM
- ✅ Close other programs
- ✅ Use `--noconfirm`

#### File quá lớn:
- ✅ Exclude unused modules
- ✅ Enable UPX compression
- ✅ Remove debug symbols
- ✅ Use `--onefile` (trade-off: slower startup)

## 📊 Benchmark Results

### Kích thước file:
- **Minimal build**: ~200MB
- **Standard build**: ~500MB
- **Full build with models**: ~1-2GB

### Thời gian build:
- **First build**: 10-20 minutes
- **Incremental**: 2-5 minutes
- **Clean build**: 5-15 minutes

### Startup time:
- **Directory mode**: 2-5 seconds
- **Onefile mode**: 10-30 seconds

## 🚀 Distribution

### Tạo portable package
```bash
# Compress thành ZIP
powershell Compress-Archive -Path "dist\PyVideoTrans" -DestinationPath "PyVideoTrans-v1.0-Portable.zip"
```

### Tạo installer
```bash
# Sử dụng NSIS
makensis installer.nsi

# Hoặc Inno Setup
iscc installer.iss
```

### Test trên máy clean
1. Copy `dist/PyVideoTrans/` sang máy không có Python
2. Run `PyVideoTrans.exe`
3. Test các chức năng chính
4. Check for missing dependencies

## 📝 Best Practices

### Development:
- ✅ Always use virtual environment
- ✅ Pin dependency versions
- ✅ Test on multiple systems
- ✅ Document build process

### Production:
- ✅ Version your builds
- ✅ Sign executables
- ✅ Test thoroughly
- ✅ Provide user documentation

### Maintenance:
- ✅ Regular dependency updates
- ✅ Security patches
- ✅ Performance monitoring
- ✅ User feedback integration

## 🆘 Getting Help

### Nếu gặp vấn đề:

1. **Check build logs:**
   - `build/PyVideoTrans/warn-PyVideoTrans.txt`
   - `build/PyVideoTrans/xref-PyVideoTrans.html`

2. **Common solutions:**
   - Reinstall Python 3.10
   - Update PyInstaller
   - Clear build cache
   - Check antivirus settings

3. **Report issues với:**
   - Python version: `py -3.10 --version`
   - OS version: `systeminfo`
   - Error logs
   - Build command used

## 📚 Resources

- [Python 3.10 Downloads](https://www.python.org/downloads/release/python-31011/)
- [PyInstaller Documentation](https://pyinstaller.readthedocs.io/)
- [PySide6 Documentation](https://doc.qt.io/qtforpython/)
- [PyVideoTrans GitHub](https://github.com/jianchang512/pyvideotrans)

---

**Happy Building with Python 3.10! 🎉**
