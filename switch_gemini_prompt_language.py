#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để chuyển đổi ngôn ngữ prompt cho Gemini translator
Switch Gemini prompt language between Chinese and English
"""

import os
import sys
from pathlib import Path
import configparser

def get_project_root():
    """Tìm thư mục gốc của dự án"""
    current_dir = Path(__file__).parent.absolute()
    
    # Tìm thư mục chứa videotrans
    while current_dir.parent != current_dir:
        if (current_dir / 'videotrans').exists():
            return current_dir
        current_dir = current_dir.parent
    
    return Path(__file__).parent.absolute()

def read_current_language():
    """Đọc ngôn ngữ hiện tại từ set.ini"""
    root_dir = get_project_root()
    ini_file = root_dir / 'set.ini'
    
    if not ini_file.exists():
        print(f"❌ File set.ini không tồn tại: {ini_file}")
        return None
    
    config = configparser.ConfigParser()
    config.read(ini_file, encoding='utf-8')
    
    try:
        lang = config.get('dev', 'lang', fallback='zh')
        return lang.lower()
    except:
        return 'zh'

def switch_language(target_lang):
    """Chuyển đổi ngôn ngữ trong set.ini"""
    root_dir = get_project_root()
    ini_file = root_dir / 'set.ini'
    
    if not ini_file.exists():
        print(f"❌ File set.ini không tồn tại: {ini_file}")
        return False
    
    # Đọc file hiện tại
    config = configparser.ConfigParser()
    config.read(ini_file, encoding='utf-8')
    
    # Đảm bảo section [dev] tồn tại
    if 'dev' not in config:
        config.add_section('dev')
    
    # Cập nhật ngôn ngữ
    config.set('dev', 'lang', target_lang)
    
    # Ghi lại file
    try:
        with open(ini_file, 'w', encoding='utf-8') as f:
            config.write(f)
        return True
    except Exception as e:
        print(f"❌ Lỗi khi ghi file: {e}")
        return False

def check_prompt_files():
    """Kiểm tra các file prompt hiện có"""
    root_dir = get_project_root()
    
    files_to_check = [
        'videotrans/gemini.txt',
        'videotrans/gemini-en.txt',
        'videotrans/prompts/srt/gemini.txt',
        'videotrans/prompts/srt/gemini-en.txt'
    ]
    
    print("\n📁 Kiểm tra file prompt:")
    for file_path in files_to_check:
        full_path = root_dir / file_path
        status = "✅ Tồn tại" if full_path.exists() else "❌ Không tồn tại"
        print(f"   {file_path}: {status}")

def create_english_prompt_if_missing():
    """Tạo file prompt tiếng Anh nếu chưa tồn tại"""
    root_dir = get_project_root()
    
    # Prompt cho translation thông thường
    en_prompt_file = root_dir / 'videotrans/gemini-en.txt'
    if not en_prompt_file.exists():
        en_prompt_content = """# Role:
You are a professional video subtitle translation specialist, dedicated to translating subtitle content into {lang}, ensuring natural fluency and adherence to target language expression conventions.

## Core Mission:
Translate the input subtitle text completely and accurately into {lang}, maintaining the original timeline and format structure.

## Translation Rules:
- **MUST TRANSLATE EVERYTHING**: Absolutely no original text should remain untranslated, even proper nouns must be given the best {lang} expression
- **STRICT LINE CORRESPONDENCE**: One source line corresponds to one translated line, never merge or split lines
- **NATURAL COLLOQUIAL**: Use natural expressions that conform to daily {lang} speech, avoid rigid literal translation
- **CONCISE AND CLEAR**: Subtitle translation should be concise and easy to understand, avoid lengthy complex sentences
- **MAINTAIN CONTEXT**: Consider contextual coherence to ensure translation continuity

## Output Format:
**MUST strictly follow the XML format below, do not add any other content:**

```xml
<TRANSLATE_TEXT>
Translated {lang} text
Each line corresponds to one line of the original
Maintain the same number of lines
</TRANSLATE_TEXT>
```

## Important Reminders:
- 🚫 Absolutely forbidden to return original text or mixed languages
- 🚫 Forbidden to add explanations, notes, or other content
- ✅ Only return pure translation results in XML format
- ✅ Ensure complete line correspondence

## Input Content:
<INPUT></INPUT>"""
        
        en_prompt_file.parent.mkdir(parents=True, exist_ok=True)
        with en_prompt_file.open('w', encoding='utf-8') as f:
            f.write(en_prompt_content)
        print(f"✅ Đã tạo file: {en_prompt_file}")
    
    # Prompt cho SRT translation
    srt_en_prompt_file = root_dir / 'videotrans/prompts/srt/gemini-en.txt'
    if not srt_en_prompt_file.exists():
        srt_en_prompt_file.parent.mkdir(parents=True, exist_ok=True)
        with srt_en_prompt_file.open('w', encoding='utf-8') as f:
            f.write(en_prompt_content)  # Sử dụng cùng content
        print(f"✅ Đã tạo file: {srt_en_prompt_file}")

def main():
    print("🔄 Gemini Prompt Language Switcher")
    print("=" * 50)
    
    # Kiểm tra ngôn ngữ hiện tại
    current_lang = read_current_language()
    if current_lang:
        lang_name = "Tiếng Trung" if current_lang == 'zh' else "Tiếng Anh"
        print(f"📍 Ngôn ngữ hiện tại: {current_lang} ({lang_name})")
    
    # Kiểm tra file prompt
    check_prompt_files()
    
    # Tạo file tiếng Anh nếu thiếu
    create_english_prompt_if_missing()
    
    print("\n🎯 Chọn hành động:")
    print("1. Chuyển sang prompt tiếng Anh (en)")
    print("2. Chuyển sang prompt tiếng Trung (zh)")
    print("3. Chỉ kiểm tra trạng thái")
    print("0. Thoát")
    
    choice = input("\nNhập lựa chọn (0-3): ").strip()
    
    if choice == '1':
        if switch_language('en'):
            print("✅ Đã chuyển sang prompt tiếng Anh!")
            print("⚠️  Vui lòng khởi động lại PyVideoTrans để áp dụng thay đổi.")
        else:
            print("❌ Không thể chuyển đổi ngôn ngữ.")
    
    elif choice == '2':
        if switch_language('zh'):
            print("✅ Đã chuyển sang prompt tiếng Trung!")
            print("⚠️  Vui lòng khởi động lại PyVideoTrans để áp dụng thay đổi.")
        else:
            print("❌ Không thể chuyển đổi ngôn ngữ.")
    
    elif choice == '3':
        print("✅ Đã kiểm tra trạng thái.")
    
    elif choice == '0':
        print("👋 Tạm biệt!")
    
    else:
        print("❌ Lựa chọn không hợp lệ.")

if __name__ == "__main__":
    main()
