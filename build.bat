@echo off
title PyVideoTrans Build Script
echo.
echo ========================================
echo   PyVideoTrans Build Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found in PATH
    echo Please install Python 3.8+ and add it to PATH
    pause
    exit /b 1
)

echo Python found. Starting build process...
echo.

REM Run the Python build script
python build_exe.py

if errorlevel 1 (
    echo.
    echo ========================================
    echo   Build Failed!
    echo ========================================
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Build Completed Successfully!
echo ========================================
echo.
echo Output location: dist\PyVideoTrans\
echo Main executable: PyVideoTrans.exe
echo User launcher: run.bat
echo.
echo The application is ready for distribution!
echo.

REM Ask if user wants to open the output folder
set /p open_folder="Open output folder? (y/n): "
if /i "%open_folder%"=="y" (
    explorer "dist\PyVideoTrans"
)

pause
