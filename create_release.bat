@echo off
echo Creating PyVideoTrans Release Package...
echo.

REM Get current date for filename
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%"

REM Create release directory
if not exist "release" mkdir release

REM Copy the built application
echo Copying built application...
xcopy "dist\pyvideotrans" "release\PyVideoTrans_%datestamp%" /E /I /H /Y

REM Create zip file (requires 7zip or similar)
echo Creating zip archive...
if exist "C:\Program Files\7-Zip\7z.exe" (
    "C:\Program Files\7-Zip\7z.exe" a -tzip "release\PyVideoTrans_%datestamp%.zip" "release\PyVideoTrans_%datestamp%\*"
    echo.
    echo Release package created: release\PyVideoTrans_%datestamp%.zip
) else (
    echo 7-Zip not found. Please manually zip the folder: release\PyVideoTrans_%datestamp%
)

echo.
echo Build Summary:
echo ==============
echo - Executable: sp.exe (~95MB)
echo - Total size: ~2-3GB (includes all dependencies)
echo - Target: Windows 10/11 64-bit
echo - Python version: 3.12.10
echo - PyInstaller version: 6.12.0
echo.
echo Files included:
echo - sp.exe (main executable)
echo - _internal/ (all dependencies and libraries)
echo - run.bat (user-friendly launcher)
echo - README_BUILD.txt (user instructions)
echo.
echo The application is ready for distribution!
echo Users only need to extract and run sp.exe
echo.
pause
