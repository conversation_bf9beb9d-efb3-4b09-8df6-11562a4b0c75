@echo off
chcp 65001 >nul
title PyVideoTrans Build Setup

echo ========================================
echo PyVideoTrans Build Setup for Python 3.10
echo ========================================
echo.

REM Check if Python 3.10 is available
echo 🐍 Checking Python 3.10...
py -3.10 --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 3.10 not found!
    echo.
    echo 💡 Please install Python 3.10:
    echo 1. Download from https://www.python.org/downloads/
    echo 2. Make sure to check "Add Python to PATH"
    echo 3. Install for all users
    echo.
    pause
    exit /b 1
)

py -3.10 --version
echo ✅ Python 3.10 found!
echo.

REM Check if we're in the right directory
if not exist "videotrans" (
    echo ❌ videotrans folder not found!
    echo Please run this script from the PyVideoTrans root directory
    pause
    exit /b 1
)

if not exist "sp.py" (
    echo ❌ sp.py not found!
    echo Please run this script from the PyVideoTrans root directory
    pause
    exit /b 1
)

echo ✅ Project files found!
echo.

REM Create virtual environment
echo 📦 Creating Python 3.10 virtual environment...
if exist "venv_build" (
    echo Removing existing build environment...
    rmdir /s /q "venv_build"
)

py -3.10 -m venv venv_build
if %errorlevel% neq 0 (
    echo ❌ Failed to create virtual environment
    pause
    exit /b 1
)

echo ✅ Virtual environment created!
echo.

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv_build\Scripts\activate.bat

REM Upgrade pip
echo 📦 Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo 📦 Installing requirements...
if exist "requirements.txt" (
    pip install -r requirements.txt
) else (
    echo ⚠️  requirements.txt not found, installing basic dependencies...
    pip install PySide6 torch numpy requests faster_whisper openai google-generativeai
)

REM Install build tools
echo 🔨 Installing build tools...
pip install pyinstaller>=5.13.0 setuptools wheel

echo.
echo ========================================
echo Setup completed! Ready to build.
echo ========================================
echo.

REM Ask user what to do next
echo Choose an option:
echo 1. Build with existing build_exe.py
echo 2. Build with Python 3.10 optimized script
echo 3. Exit and build manually
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🔨 Building with existing script...
    python build_exe.py
) else if "%choice%"=="2" (
    echo.
    echo 🔨 Building with Python 3.10 optimized script...
    python build_exe_python310.py
) else (
    echo.
    echo 💡 To build manually, run:
    echo    venv_build\Scripts\activate.bat
    echo    python build_exe.py
    echo    or
    echo    python build_exe_python310.py
)

echo.
echo Build process completed!
pause
