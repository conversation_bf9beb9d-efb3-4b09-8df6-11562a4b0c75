# run again.  Do not edit this file unless you know what you are doing.


from PySide6 import QtCore, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QPlainTextEdit

from videotrans.configure import config
from videotrans.util import tools


class Ui_recognapiform(object):
    def setupUi(self, recognapiform):
        self.has_done = False
        recognapiform.setObjectName("recognapiform")
        recognapiform.setWindowModality(QtCore.Qt.NonModal)
        recognapiform.resize(500, 400)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(recognapiform.sizePolicy().hasHeightForWidth())
        recognapiform.setSizePolicy(sizePolicy)

        self.verticalLayout = QtWidgets.QVBoxLayout(recognapiform)
        self.verticalLayout.setObjectName("verticalLayout")

        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_2.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(recognapiform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(100, 35))
        self.label.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.recognapiform_address = QtWidgets.QLineEdit(recognapiform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.recognapiform_address.sizePolicy().hasHeightForWidth())
        self.recognapiform_address.setSizePolicy(sizePolicy)
        self.recognapiform_address.setMinimumSize(QtCore.QSize(210, 35))
        self.recognapiform_address.setObjectName("recognapiform_address")

        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.recognapiform_address)
        self.verticalLayout.addLayout(self.formLayout_2)

        # sk
        self.formLayout_3 = QtWidgets.QFormLayout()
        self.formLayout_3.setSizeConstraint(QtWidgets.QLayout.SetMinimumSize)
        self.formLayout_3.setFormAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.formLayout_3.setObjectName("formLayout_3")
        self.labelkey = QtWidgets.QLabel(recognapiform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.labelkey.sizePolicy().hasHeightForWidth())
        self.labelkey.setSizePolicy(sizePolicy)
        self.labelkey.setMinimumSize(QtCore.QSize(100, 35))
        self.labelkey.setAlignment(QtCore.Qt.AlignJustify | QtCore.Qt.AlignVCenter)
        self.labelkey.setObjectName("label")

        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.labelkey)
        self.recognapiform_key = QtWidgets.QLineEdit(recognapiform)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.recognapiform_key.sizePolicy().hasHeightForWidth())
        self.recognapiform_key.setSizePolicy(sizePolicy)
        self.recognapiform_key.setMinimumSize(QtCore.QSize(210, 35))
        self.recognapiform_key.setObjectName("recognapiform_key")

        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.recognapiform_key)
        self.verticalLayout.addLayout(self.formLayout_3)



        self.set = QtWidgets.QPushButton(recognapiform)
        self.set.setMinimumSize(QtCore.QSize(0, 35))
        self.set.setObjectName("set")

        self.test = QtWidgets.QPushButton(recognapiform)
        self.test.setMinimumSize(QtCore.QSize(0, 30))
        self.test.setObjectName("test")

        help_btn = QtWidgets.QPushButton()
        help_btn.setMinimumSize(QtCore.QSize(0, 35))
        help_btn.setStyleSheet("background-color: rgba(255, 255, 255,0)")
        help_btn.setObjectName("help_btn")
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setText("查看填写教程" if config.defaulelang == 'zh' else "Fill out the tutorial")
        help_btn.clicked.connect(lambda: tools.open_url(url='https://pyvideotrans.com/recognapi'))

        self.ask = QPlainTextEdit(recognapiform)
        self.ask.setMinimumSize(QtCore.QSize(0, 200))
        self.ask.setReadOnly(True)
        self.verticalLayout.addWidget(self.ask)

        self.layout_btn = QtWidgets.QHBoxLayout()
        self.layout_btn.setObjectName("layout_btn")

        self.layout_btn.addWidget(self.set)
        self.layout_btn.addWidget(self.test)
        self.layout_btn.addWidget(help_btn)

        self.verticalLayout.addLayout(self.layout_btn)

        self.retranslateUi(recognapiform)
        QtCore.QMetaObject.connectSlotsByName(recognapiform)

    def retranslateUi(self, recognapiform):
        recognapiform.setWindowTitle("自定义语音识别API" if config.defaulelang == 'zh' else 'Custom Speech Recognition API')

        self.label.setText('API' if config.defaulelang == 'zh' else 'API')
        self.labelkey.setText('密钥密码等' if config.defaulelang == 'zh' else 'Password/Token')
        tips = """
        请求发送：以二进制形式发送键名为 audio 的wav格式音频数据，采样率为16k、通道为1
        
        如果填写了密钥密码值，则附加到api_url之后发送，api_url?sk=填写的sk值
        
        requests.post(api_url, files={"audio": open(audio_file, 'rb')})
        
        失败时返回
        res={
            "code":1,
            "msg":"错误原因"
        }
        
        成功时返回
        res={
            "code":0,
            "data":[
                {
                    "text":"字幕文字",
                    "time":'00:00:01,000 --> 00:00:06,500'
                },
                {
                    "text":"字幕文字",
                    "time":'00:00:06,900 --> 00:00:12,200'
                },
            ]
        }
        """
        if config.defaulelang != 'zh':
            tips = '''
            
            Request send: sends audio data in wav format with key name audio in binary form, sample rate 16k, channel 1
            
            If the Password value is filled in, it will be sent after appending it to the api_url, api_url?sk=filled sk value
            
        requests.post(api_url, files={“audio”: open(audio_file, 'rb')})
        
        Returns on failure
        res={
            “code":1,
            “msg": ”Reason for error”
        }
        
        Returns on success
        res={
            “code":0,
            “data":[
                {
                    “text": ‘Subtitle Text’,
                    “time":‘00:00:01,000 --> 00:00:06,500’
                },
                {
                    “text": ‘Subtitle text’,
                    “time":‘00:00:06,900 --> 00:00:12,200’
                }, }
            ]
        }
            '''
        self.ask.setPlainText(tips)
        self.recognapiform_address.setPlaceholderText('http api')
        self.set.setText('保存' if config.defaulelang == 'zh' else 'Save')
        self.test.setText('测试' if config.defaulelang == 'zh' else 'Test')
