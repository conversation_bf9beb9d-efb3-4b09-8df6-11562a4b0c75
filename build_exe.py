#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để build PyVideoTrans thành file exe
Build PyVideoTrans to executable file
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path

def check_requirements():
    """Kiểm tra các yêu cầu cần thiết"""
    print("🔍 Checking requirements...")
    
    # Kiểm tra Python version
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor < 8:
        print(f"❌ Python 3.8+ required, current: {python_version.major}.{python_version.minor}")
        return False
    print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Kiểm tra PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller installed")
    
    # Kiểm tra các dependencies quan trọng
    required_packages = [
        'PySide6', 'torch', 'numpy', 'requests', 'faster_whisper'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} not found")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please install them with: pip install -r requirements.txt")
        return False
    
    return True

def clean_build_dirs():
    """Dọn dẹp thư mục build cũ"""
    print("\n🧹 Cleaning old build directories...")
    
    dirs_to_clean = ['build', 'dist']
    for dir_name in dirs_to_clean:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✅ Removed {dir_name}/")
    
    # Dọn dẹp file .spec cũ nếu cần
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        if spec_file.name != 'pyvideotrans.spec':
            spec_file.unlink()
            print(f"✅ Removed {spec_file}")

def create_improved_spec():
    """Tạo file .spec cải tiến"""
    print("\n📝 Creating improved .spec file...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import os
import sys
from pathlib import Path

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define paths
videotrans_path = os.path.join(current_dir, 'videotrans')
ffmpeg_path = os.path.join(current_dir, 'ffmpeg')

# Enhanced hidden imports
hidden_imports = [
    # Core videotrans modules
    'videotrans',
    'videotrans.configure.config',
    'videotrans.mainwin._main_win',
    'videotrans.ui.dark.darkstyle_rc',
    
    # PySide6 modules
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtMultimedia',
    'PySide6.QtNetwork',
    
    # Standard library
    'multiprocessing',
    'queue',
    'threading',
    'json',
    'pathlib',
    'tempfile',
    'logging',
    'datetime',
    'locale',
    'sys',
    'os',
    're',
    'struct',
    'time',
    'platform',
    'subprocess',
    'shutil',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'urllib.error',
    'ssl',
    'socket',
    'hashlib',
    'base64',
    'uuid',
    'configparser',
    'zipfile',
    'tarfile',
    
    # Third-party packages
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.exceptions',
    'numpy',
    'torch',
    'torch.nn',
    'torch.nn.functional',
    'torchaudio',
    'faster_whisper',
    'openai',
    'edge_tts',
    'pydub',
    'librosa',
    'soundfile',
    'PIL',
    'PIL.Image',
    'cv2',
    'srt',
    'zhconv',
    'azure.cognitiveservices.speech',
    'google.cloud.texttospeech',
    'google.generativeai',
    'google.ai.generativelanguage',
    'elevenlabs',
    'anthropic',
    'ctranslate2',
    'huggingface_hub',
    'modelscope',
    'datasets',
    'gradio_client',
    'flask',
    'waitress',
    'aiohttp',
    'httpx',
    'certifi',
    'deepl',
    'gtts',
    'pygame',
    'pytz',
    'yaml',
    'pydantic',
    'plyer',
    'py7zr',
    'deepgram',
    'funasr',
    'addict',
    'simplejson',
    'sortedcontainers',
    'alibabacloud_alimt20181012',
    'samplerate',
    
    # Additional imports for stability
    'pkg_resources',
    'pkg_resources.py2_warn',
    'packaging',
    'packaging.version',
    'packaging.specifiers',
    'packaging.requirements',
]

# Data files to include
datas = [
    (os.path.join(videotrans_path, 'styles'), 'videotrans/styles'),
    (os.path.join(videotrans_path, 'language'), 'videotrans/language'),
    (os.path.join(videotrans_path, 'ui'), 'videotrans/ui'),
    (os.path.join(videotrans_path, 'prompts'), 'videotrans/prompts'),
    (os.path.join(current_dir, 'voice_list.json'), '.'),
    (os.path.join(current_dir, 'azure_voice_list.json'), '.'),
    (os.path.join(current_dir, 'version.json'), '.'),
    (os.path.join(current_dir, 'params.json.example'), '.'),
]

# Include ffmpeg binaries if they exist
if os.path.exists(ffmpeg_path):
    for file in os.listdir(ffmpeg_path):
        if file.endswith('.exe') or file.endswith('.txt'):
            datas.append((os.path.join(ffmpeg_path, file), 'ffmpeg'))

# Include all non-Python files in videotrans directory
for root, dirs, files in os.walk(videotrans_path):
    for file in files:
        if not file.endswith('.py') and not file.endswith('.pyc') and not file.startswith('.'):
            full_path = os.path.join(root, file)
            rel_path = os.path.relpath(root, current_dir)
            datas.append((full_path, rel_path))

# Analysis configuration
a = Analysis(
    ['sp.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'notebook',
        'IPython',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate files
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='PyVideoTrans',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # GUI application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(videotrans_path, 'styles', 'icon.ico') if os.path.exists(os.path.join(videotrans_path, 'styles', 'icon.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PyVideoTrans',
)
'''
    
    with open('pyvideotrans_improved.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Created pyvideotrans_improved.spec")

def build_executable():
    """Build executable với PyInstaller"""
    print("\n🔨 Building executable with PyInstaller...")
    
    # Sử dụng spec file cải tiến
    spec_file = 'pyvideotrans_improved.spec'
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        spec_file
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed with error code {e.returncode}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
        return False

def create_launcher_script():
    """Tạo script launcher thân thiện với người dùng"""
    print("\n📝 Creating user-friendly launcher...")
    
    launcher_content = '''@echo off
title PyVideoTrans
echo Starting PyVideoTrans...
echo.

REM Check if executable exists
if not exist "PyVideoTrans.exe" (
    echo Error: PyVideoTrans.exe not found!
    echo Please make sure you extracted all files correctly.
    pause
    exit /b 1
)

REM Start the application
start "" "PyVideoTrans.exe"

REM Optional: Keep console open for debugging
REM PyVideoTrans.exe
REM pause
'''
    
    launcher_path = Path('dist/PyVideoTrans/run.bat')
    launcher_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"✅ Created launcher: {launcher_path}")

def create_readme():
    """Tạo file README cho người dùng"""
    print("\n📝 Creating README for users...")
    
    readme_content = '''# PyVideoTrans - Portable Version

## Quick Start
1. Double-click `run.bat` to start the application
2. Or directly run `PyVideoTrans.exe`

## System Requirements
- Windows 10/11 (64-bit)
- At least 4GB RAM (8GB recommended)
- 2GB free disk space

## First Time Setup
1. The application will create necessary folders on first run
2. Configure your API keys in Settings menu
3. Download required models as needed

## Troubleshooting
- If the app doesn't start, try running `PyVideoTrans.exe` directly
- Check Windows Defender/Antivirus settings if blocked
- Ensure all files were extracted properly

## Features
- Video translation with multiple AI services
- Speech recognition and synthesis
- Subtitle generation and editing
- Multiple language support

## Support
- Documentation: https://pyvideotrans.com
- GitHub: https://github.com/jianchang512/pyvideotrans
- Issues: Report bugs on GitHub

Built with PyInstaller
'''
    
    readme_path = Path('dist/PyVideoTrans/README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ Created README: {readme_path}")

def main():
    """Main build process"""
    print("🚀 PyVideoTrans Build Script")
    print("=" * 50)
    
    # Kiểm tra requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        return False
    
    # Dọn dẹp
    clean_build_dirs()
    
    # Tạo spec file cải tiến
    create_improved_spec()
    
    # Build executable
    if not build_executable():
        print("\n❌ Build failed. Check the error messages above.")
        return False
    
    # Tạo các file hỗ trợ
    create_launcher_script()
    create_readme()
    
    # Thông báo hoàn thành
    print("\n🎉 Build completed successfully!")
    print("=" * 50)
    print(f"📁 Output directory: {Path('dist/PyVideoTrans').absolute()}")
    print(f"🚀 Executable: PyVideoTrans.exe")
    print(f"📝 Launcher: run.bat")
    print(f"📖 README: README.txt")
    
    # Kiểm tra kích thước
    exe_path = Path('dist/PyVideoTrans/PyVideoTrans.exe')
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"📊 Executable size: {size_mb:.1f} MB")
    
    print("\n✅ Ready for distribution!")
    print("Users can extract and run the application without Python installed.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
