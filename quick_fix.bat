@echo off
chcp 65001 >nul
title Quick Fix for PyVideoTrans Import Errors

echo ========================================
echo Quick Fix for PyVideoTrans Import Errors
echo ========================================
echo.

echo 🔧 Fixing "ModuleNotFoundError: No module named 'requests'" error...
echo.

REM Check Python
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Upgrade pip first
echo 📦 Upgrading pip...
python -m pip install --upgrade pip

REM Install requests and related packages
echo 📦 Installing requests and dependencies...
python -m pip install requests urllib3 certifi charset-normalizer idna --upgrade --force-reinstall

REM Install PySide6
echo 📦 Installing PySide6...
python -m pip install PySide6 --upgrade

REM Install numpy
echo 📦 Installing numpy...
python -m pip install numpy --upgrade

REM Install other essential packages
echo 📦 Installing other essential packages...
python -m pip install torch faster-whisper openai google-generativeai pydub librosa soundfile opencv-python Pillow srt edge-tts

echo.
echo 🧪 Testing imports...

REM Test requests
python -c "import requests; print('✅ requests imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ requests import failed
) else (
    echo ✅ requests OK
)

REM Test PySide6
python -c "import PySide6.QtWidgets; print('✅ PySide6 imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ PySide6 import failed
) else (
    echo ✅ PySide6 OK
)

REM Test numpy
python -c "import numpy; print('✅ numpy imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ numpy import failed
) else (
    echo ✅ numpy OK
)

REM Test videotrans
python -c "import videotrans; print('✅ videotrans imported successfully')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  videotrans import failed (this is normal if not in the right directory)
) else (
    echo ✅ videotrans OK
)

echo.
echo ========================================
echo Fix completed!
echo ========================================
echo.

echo 🚀 You can now try to run PyVideoTrans:
echo.
echo Option 1: python sp.py
echo Option 2: python start_fixed.py (if available)
echo.

echo 💡 If you still get import errors:
echo 1. Restart your terminal/command prompt
echo 2. Make sure you're in the PyVideoTrans directory
echo 3. Try running: python fix_requests_error.py
echo.

pause
