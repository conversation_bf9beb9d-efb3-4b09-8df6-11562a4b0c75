#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để sửa lỗi "ModuleNotFoundError: No module named 'requests'"
Fix "ModuleNotFoundError: No module named 'requests'" error
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_environment():
    """Kiểm tra môi trường Python"""
    print("🐍 Checking Python environment...")
    
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Python path: {sys.path[:3]}...")  # Show first 3 paths
    
    return True

def install_requests():
    """Cài đặt requests và dependencies"""
    print("\n📦 Installing requests and dependencies...")
    
    packages = [
        "requests",
        "urllib3", 
        "certifi",
        "charset-normalizer",
        "idna"
    ]
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                package, "--upgrade", "--force-reinstall"
            ], check=True, capture_output=True, text=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    return True

def install_all_requirements():
    """Cài đặt tất cả requirements"""
    print("\n📦 Installing all requirements...")
    
    # Try to install from requirements.txt
    if Path("requirements.txt").exists():
        try:
            print("Installing from requirements.txt...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "-r", "requirements.txt", "--upgrade"
            ], check=True, capture_output=True, text=True)
            print("✅ Requirements installed from requirements.txt")
            return True
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Failed to install from requirements.txt: {e}")
    
    # Install essential packages manually
    essential_packages = [
        "requests>=2.31.0",
        "PySide6>=6.5.0",
        "numpy>=1.24.0",
        "torch>=2.0.0",
        "faster-whisper>=0.9.0",
        "openai>=1.0.0",
        "google-generativeai>=0.3.0",
        "pydub>=0.25.0",
        "librosa>=0.10.0",
        "soundfile>=0.12.0",
        "opencv-python>=4.8.0",
        "Pillow>=10.0.0",
        "srt>=3.5.0",
        "edge-tts>=6.1.0",
    ]
    
    print("Installing essential packages...")
    for package in essential_packages:
        try:
            print(f"Installing {package}...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True, text=True)
            print(f"✅ {package}")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Failed to install {package}: {e}")
            continue
    
    return True

def test_imports():
    """Test các imports quan trọng"""
    print("\n🧪 Testing imports...")
    
    test_imports = [
        ("requests", "requests"),
        ("PySide6.QtWidgets", "PySide6.QtWidgets"),
        ("PySide6.QtCore", "PySide6.QtCore"),
        ("numpy", "numpy"),
        ("videotrans", "videotrans"),
    ]
    
    failed_imports = []
    
    for import_name, display_name in test_imports:
        try:
            __import__(import_name)
            print(f"✅ {display_name}")
        except ImportError as e:
            print(f"❌ {display_name}: {e}")
            failed_imports.append(import_name)
    
    if failed_imports:
        print(f"\n⚠️  Failed imports: {', '.join(failed_imports)}")
        return False
    else:
        print("\n✅ All imports successful!")
        return True

def fix_python_path():
    """Sửa Python path"""
    print("\n🔧 Fixing Python path...")
    
    current_dir = Path.cwd()
    videotrans_dir = current_dir / "videotrans"
    
    # Add to sys.path
    paths_to_add = [str(current_dir), str(videotrans_dir)]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
            print(f"✅ Added {path} to Python path")
    
    return True

def create_startup_fix():
    """Tạo script khởi động với fix"""
    print("\n📝 Creating startup fix script...")
    
    startup_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PyVideoTrans startup script with import fixes
"""

import sys
import os
from pathlib import Path

def fix_imports():
    """Fix import issues"""
    # Add current directory to path
    current_dir = Path(__file__).parent.absolute()
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Add videotrans directory
    videotrans_dir = current_dir / "videotrans"
    if videotrans_dir.exists() and str(videotrans_dir) not in sys.path:
        sys.path.insert(0, str(videotrans_dir))

def test_critical_imports():
    """Test critical imports before starting"""
    try:
        import requests
        print("✅ requests imported successfully")
    except ImportError:
        print("❌ requests not found. Installing...")
        import subprocess
        subprocess.run([sys.executable, "-m", "pip", "install", "requests"])
        import requests
        print("✅ requests installed and imported")
    
    try:
        import PySide6.QtWidgets
        print("✅ PySide6 imported successfully")
    except ImportError:
        print("❌ PySide6 not found. Installing...")
        import subprocess
        subprocess.run([sys.executable, "-m", "pip", "install", "PySide6"])
        import PySide6.QtWidgets
        print("✅ PySide6 installed and imported")

def main():
    """Main function"""
    print("🚀 Starting PyVideoTrans with import fixes...")
    
    # Fix imports
    fix_imports()
    
    # Test critical imports
    test_critical_imports()
    
    try:
        # Import and run main application
        import sp
        print("✅ Application started successfully")
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("start_fixed.py", "w", encoding="utf-8") as f:
            f.write(startup_content)
        print("✅ Created start_fixed.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create startup script: {e}")
        return False

def create_install_script():
    """Tạo script cài đặt dependencies"""
    print("\n📝 Creating install script...")
    
    install_content = '''@echo off
chcp 65001 >nul
title Fix PyVideoTrans Import Errors

echo ========================================
echo Fix PyVideoTrans Import Errors
echo ========================================
echo.

echo 🔧 Fixing import errors...
echo.

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install requests and dependencies
echo Installing requests...
python -m pip install requests urllib3 certifi charset-normalizer idna --upgrade --force-reinstall

REM Install PySide6
echo Installing PySide6...
python -m pip install PySide6 --upgrade

REM Install other essential packages
echo Installing essential packages...
python -m pip install numpy torch faster-whisper openai google-generativeai

REM Test imports
echo.
echo Testing imports...
python -c "import requests; print('✅ requests OK')"
python -c "import PySide6.QtWidgets; print('✅ PySide6 OK')"
python -c "import numpy; print('✅ numpy OK')"

echo.
echo ✅ Import fixes completed!
echo.
echo You can now run:
echo   python start_fixed.py
echo   or
echo   python sp.py
echo.

pause
'''
    
    try:
        with open("fix_imports.bat", "w", encoding="utf-8") as f:
            f.write(install_content)
        print("✅ Created fix_imports.bat")
        return True
    except Exception as e:
        print(f"❌ Failed to create install script: {e}")
        return False

def main():
    """Main function"""
    print("🔧 PyVideoTrans Requests Error Fixer")
    print("=" * 60)
    
    print("This script will fix the 'ModuleNotFoundError: No module named requests' error")
    print()
    
    # Check if we're in the right directory
    if not Path('videotrans').exists() or not Path('sp.py').exists():
        print("❌ Please run this script from the PyVideoTrans root directory")
        return False
    
    # Fix steps
    steps = [
        ("Check Python Environment", check_python_environment),
        ("Fix Python Path", fix_python_path),
        ("Install Requests", install_requests),
        ("Install All Requirements", install_all_requirements),
        ("Test Imports", test_imports),
        ("Create Startup Fix", create_startup_fix),
        ("Create Install Script", create_install_script),
    ]
    
    results = {}
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            results[step_name] = result
            if not result and step_name in ["Install Requests", "Test Imports"]:
                print(f"⚠️  {step_name} failed, but continuing...")
        except Exception as e:
            print(f"❌ {step_name} failed with exception: {e}")
            results[step_name] = False
    
    # Summary
    print("\n" + "="*60)
    print("📊 FIX SUMMARY")
    print("="*60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for step_name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{step_name:<25} {status}")
    
    print("="*60)
    print(f"Results: {passed}/{total} fixes applied")
    
    if results.get("Test Imports", False):
        print("\n🎉 Import errors fixed successfully!")
        print("\n🚀 You can now run PyVideoTrans:")
        print("1. python start_fixed.py  (recommended)")
        print("2. python sp.py")
        
    else:
        print("\n⚠️  Some import issues remain. Try these manual steps:")
        print("\n🔧 Manual fixes:")
        print("1. Run: fix_imports.bat")
        print("2. Or manually: pip install requests PySide6 numpy")
        print("3. Restart your terminal/IDE")
        print("4. Try: python start_fixed.py")
        
        print("\n💡 If still having issues:")
        print("1. Check Python installation")
        print("2. Try using virtual environment")
        print("3. Check antivirus/firewall settings")
    
    return results.get("Test Imports", False)

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Please run fix_imports.bat or install dependencies manually")
        sys.exit(1)
