English | [简体中文](README.md) | [pt-BR](docs/pt-BR/README_pt-BR.md) | [Italian](docs/IT/README_IT.md) | [Spanish](docs/ES/README_ES.md) / [Donate](docs/about.md) / [Discord](https://discord.gg/y9gUweVCCJ) / WeChat: `pyvideotrans`

# Video Translation and Dubbing Tool

This is a video translation and dubbing tool that can translate videos from one language to another, automatically generating and adding subtitles and dubbing in the target language. It also supports API calls.

Speech recognition supports `faster-whisper` and `openai-whisper` local offline models, as well as `OpenAI SpeechToText API`, `GoogleSpeech`, `Alibaba Chinese Speech Recognition Model`, and Doubao model, with support for custom speech recognition APIs.

Text translation supports `Microsoft Translator|Google Translate|Baidu Translate|Tencent Translate|ChatGPT|AzureAI|Gemini|DeepL|DeepLX|ByteDance Volcano|Offline Translation OTT`

Text-to-speech synthesis supports `Microsoft Edge TTS`, `Google TTS`, `Azure AI TTS`, `OpenAI TTS`, `Elevenlabs TTS`, `Custom TTS Server API`, `GPT-SoVITS`, [clone-voice](https://github.com/jianchang512/clone-voice), [ChatTTS-ui](https://github.com/jianchang512/ChatTTS-ui), [Fish TTS](https://github.com/fishaudio/fish-speech), [CosyVoice](https://github.com/FunAudioLLM/CosyVoice)

Allows preservation of background music and accompaniment (based on uvr5)

Supported languages: Chinese (Simplified/Traditional), English, Korean, Japanese, Russian, French, German, Italian, Spanish, Portuguese, Vietnamese, Thai, Arabic, Turkish, Hungarian, Hindi, Ukrainian, Kazakh, Indonesian, Malay, Czech, Polish, Dutch, Swedish / Other languages with automatic detection option

> **[Sponsor]**
> 
> [![](https://github.com/user-attachments/assets/5348c86e-2d5f-44c7-bc1b-3cc5f077e710)](https://gpt302.saaslink.net/teRK8Y)
>  [302.AI](https://gpt302.saaslink.net/teRK8Y) is a pay-as-you-go one-stop AI application platform, open platform, open source ecosystem, [302.AI Open Source](https://gpt302.saaslink.net/teRK8Y)
> 
> Integrates the latest and most comprehensive AI models and brands / Pay-as-you-go with zero monthly fees / Separation of management and usage / All AI capabilities provide APIs / 2-3 new applications launched weekly

# Main Uses and Features

【Automatic Video Translation and Dubbing】Translate the audio in videos to dubbing in another language and embed subtitles in that language

【Speech Recognition/Convert Audio Video to Subtitles】Batch convert human speech in audio and video files to text and export as SRT subtitle files

【Speech Synthesis/Subtitle Dubbing】Create dubbing based on existing local SRT subtitle files, supporting single or batch subtitles

【Translate Subtitle Files】Translate one or more SRT subtitle files to subtitle files in other languages

【Merge Video and Audio】Batch merge video files and audio files in one-to-one correspondence

【Merge Video and SRT Subtitles】Batch merge video files and SRT subtitle files in one-to-one correspondence

【Add Image Watermarks to Videos】Batch embed image watermarks into video files

【Extract Audio from Videos】Separate videos into audio files and silent videos

【Audio Video Format Conversion】Batch convert audio and video formats

【Subtitle Editing and Multi-format Export】Support importing SRT, VTT, ASS format subtitles, edit and set font styles, colors, etc., then export corresponding format subtitles

【Subtitle Format Conversion】Batch convert subtitle files between SRT/ASS/VTT formats

【Download YouTube Videos】Download videos from YouTube

【Vocal and Background Music Separation】

【API Calls】Support speech synthesis, speech recognition, subtitle translation, and video translation API calls

----

![pyvideotrans-home](https://github.com/user-attachments/assets/b2f95a7f-b4e5-4a6d-b2a5-eb6cd22531e0)

[![Open In Colab](https://img.shields.io/badge/Colab-F9AB00?style=for-the-badge&logo=googlecolab&color=525252)](https://colab.research.google.com/drive/1kPTeAMz3LnWRnGmabcz4AWW42hiehmfm?usp=sharing)

# Pre-packaged Version (Only for Win10/Win11, MacOS/Linux systems use source code deployment)

> Packaged using pyinstaller, without virus exemption and signing, antivirus software may report false positives. Please add to trust list or use source code deployment

0. [Click to download pre-packaged version, extract to an English directory without spaces, then double-click sp.exe](https://github.com/jianchang512/pyvideotrans/releases)

1. Extract to an English path without spaces. After extraction, double-click sp.exe (If you encounter permission issues, right-click and run as administrator)

4. Note: Must be used after extraction, cannot be used directly by double-clicking within the compressed package, and cannot move sp.exe file to other locations after extraction

# MacOS Source Code Deployment

0. Open terminal window and execute the following commands
	
	> Before execution, ensure Homebrew is installed. If you don't have Homebrew installed, you need to install it first
	>
	> Execute command to install Homebrew: `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`
	>
	> After installation, execute: `eval $(brew --config)`
	>

    ```
    brew install libsndfile

    brew install ffmpeg

    brew install git

    brew install python@3.10

    ```

    Continue executing

    ```
    export PATH="/usr/local/opt/python@3.10/bin:$PATH"

    source ~/.bash_profile 
	
	source ~/.zshrc

    ```

1. Create a folder without spaces and Chinese characters, enter this folder in terminal.
2. Execute command in terminal `git clone https://github.com/jianchang512/pyvideotrans `
3. Execute command `cd pyvideotrans`
4. Continue executing `python -m venv venv`
5. Continue executing command `source ./venv/bin/activate`, after execution check and confirm that the terminal command prompt has changed to start with `(venv)`, the following commands must ensure the terminal prompt starts with `(venv)`
6. Execute `pip install -r requirements.txt `, if it fails, execute the following 2 commands to switch pip mirror to Alibaba mirror

    ```
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
    pip config set install.trusted-host mirrors.aliyun.com
    ```

    Then re-execute
    If you have switched to Alibaba mirror source and still get failure, please try executing `pip install -r requirements.txt`

7. `python sp.py` to open the software interface

# Linux Source Code Deployment

0. For CentOS/RHEL systems, execute the following commands to install python3.10

```

sudo yum update

sudo yum groupinstall "Development Tools"

sudo yum install openssl-devel bzip2-devel libffi-devel

cd /tmp

wget https://www.python.org/ftp/python/3.10.4/Python-3.10.4.tgz

tar xzf Python-3.10.4.tgz

cd Python-3.10.4

./configure — enable-optimizations

sudo make && sudo make install

sudo alternatives — install /usr/bin/python3 python3 /usr/local/bin/python3.10 1

sudo yum install -y ffmpeg

```

1. For Ubuntu/Debian systems, execute the following commands to install python3.10

```

apt update && apt upgrade -y

apt install software-properties-common -y

add-apt-repository ppa:deadsnakes/ppa

apt update

sudo apt-get install libxcb-cursor0

apt install python3.10

curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10

sudo update-alternatives --install /usr/bin/python python /usr/local/bin/python3.10  1

sudo update-alternatives --config python

apt-get install ffmpeg

```

**Open any terminal and execute `python3 -V`, if it displays "3.10.4", the installation is successful, otherwise it failed**

1. Create a folder without spaces and Chinese characters, open this folder from terminal.
3. Execute command in terminal `git clone https://github.com/jianchang512/pyvideotrans`
4. Continue executing command `cd pyvideotrans`
5. Continue executing `python -m venv venv`
6. Continue executing command `source .\venv\scripts\activate`, after execution check and confirm that the terminal command prompt has changed to start with `(venv)`, the following commands must ensure the terminal prompt starts with `(venv)`
7. Execute `pip install -r requirements.txt`, if it fails, execute the following 2 commands to switch pip mirror to Alibaba mirror

    ```

    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
    pip config set install.trusted-host mirrors.aliyun.com

    ```

    Then re-execute, if you have switched to Alibaba mirror source and still get failure, please try executing `pip install -r requirements.txt `
8. If you want to use CUDA acceleration, execute separately

    `pip uninstall -y torch torchaudio`

    `pip install torch==2.2.0 torchaudio==2.2.0 --index-url https://download.pytorch.org/whl/cu118`

    `pip install nvidia-cublas-cu11 nvidia-cudnn-cu11`

9. For Linux to enable CUDA acceleration, you must have an NVIDIA graphics card and have configured CUDA11.8+ environment, please search for "Linux CUDA installation" yourself

10. `python sp.py` to open the software interface

# Windows 10/11 Source Code Deployment

0. Open https://www.python.org/downloads/ to download Windows Python 3.10, after downloading double-click and follow next steps, make sure to check "Add to PATH"

   **Open a cmd and execute `python -V`, if the output is not `3.10.4`, it means installation failed or not added to `Add to PATH`, please reinstall**

1. Open https://github.com/git-for-windows/git/releases/download/v2.45.0.windows.1/Git-2.45.0-64-bit.exe to download git, after downloading double-click and follow next steps.
2. Find a folder without spaces and Chinese characters, enter `cmd` in the address bar and press Enter to open terminal, all following commands are executed in this terminal
3. Execute command `git clone https://github.com/jianchang512/pyvideotrans`
4. Continue executing command `cd pyvideotrans`
5. Continue executing `python -m venv venv`
6. Continue executing command `venv\Scripts\activate`, after execution check and confirm that the command line starts with `(venv)`, otherwise it indicates an error
7. Execute `pip install -r requirements.txt `, if it fails, execute the following 2 commands to switch pip mirror to Alibaba mirror

    ```

    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
    pip config set install.trusted-host mirrors.aliyun.com

    ```

    Then re-execute, if you have switched to Alibaba mirror source and still get failure, please try executing `pip install -r requirements.txt`
8.  If you want to use CUDA acceleration, execute separately

    `pip uninstall -y torch torchaudio`

    `pip install torch==2.2.0 torchaudio==2.2.0 --index-url https://download.pytorch.org/whl/cu118`

9. For Windows to enable CUDA acceleration, you must have an NVIDIA graphics card and have configured CUDA11.8+ environment, for specific installation see [CUDA Acceleration Support](https://pyvideotrans.com/gpu.html)

10. Extract ffmpeg.zip to the current source code directory, overwrite when prompted, after extraction ensure you can see ffmpeg.exe ffprobe.exe ytwin32.exe in the ffmpeg folder under the source code,

11. `python sp.py` to open the software interface

# Source Code Deployment Issues

1. By default uses ctranslate2 version 4.x, which only supports CUDA12.x version. If your CUDA is lower than 12 and cannot be upgraded to 12.x, please execute commands to uninstall ctranslate2 and reinstall

```

pip uninstall -y ctranslate2

pip install ctranslate2==3.24.0

```

2. You may encounter `xx module not found` errors. Please open requirements.txt, search for the xx module, then remove the == and version number after xx

# Usage Tutorials and Documentation

Please visit https://pyvideotrans.com

# Speech Recognition Models:

   Download link: https://pyvideotrans.com/model.html

# Video Tutorials (Third-party)

[MacOS Source Code Deployment/Bilibili](https://www.bilibili.com/video/BV1tK421y7rd/)

[How to Set Up Video Translation with Gemini API/Bilibili](https://b23.tv/fED1dS3)

[How to Download and Install](https://www.bilibili.com/video/BV1Gr421s7cN/)

# Software Preview Screenshots

![pyvideotrans-home](https://github.com/user-attachments/assets/b2f95a7f-b4e5-4a6d-b2a5-eb6cd22531e0)

![image](https://github.com/user-attachments/assets/b5d1b5fb-c579-477c-bca4-6c5e9aa14d7d)

# Related Projects

[ChatTTS-ui: UI interface for synthesizing speech using ChatTTS](https://github.com/jianchang512/ChatTTS-ui)

[OTT: Local offline text translation tool](https://github.com/jianchang512/ott)

[Voice Cloning Tool: Synthesize speech with any voice](https://github.com/jianchang512/clone-voice)

[Speech Recognition Tool: Local offline speech-to-text tool](https://github.com/jianchang512/stt)

[Vocal and Background Music Separation: Tool for separating vocals and background music](https://github.com/jianchang512/vocal-separate)

[Improved version of GPT-SoVITS api.py](https://github.com/jianchang512/gptsovits-api)

[api.py adapted for CosyVoice](https://github.com/jianchang512/cosyvoice-api)

## Acknowledgments

> Main open source projects this program depends on

1. [ffmpeg](https://github.com/FFmpeg/FFmpeg)
2. [PySide6](https://pypi.org/project/PySide6/)
3. [edge-tts](https://github.com/rany2/edge-tts)
4. [faster-whisper](https://github.com/SYSTRAN/faster-whisper)
5. [openai-whisper](https://github.com/openai/whisper)
6. [pydub](https://github.com/jiaaro/pydub)

## Follow Author's WeChat Official Account

<img width="200" src="https://github.com/jianchang512/pyvideotrans/assets/3378335/f9337111-9084-41fe-8840-1fb8fedca92d">

If you find this project valuable and hope it can be maintained stably and continuously, donations are welcome

<img width="200" src="https://github.com/user-attachments/assets/5e8688ef-47c3-4a3c-a016-e60f73ccc4dc">

<img width="200" src="https://github.com/jianchang512/pyvideotrans/assets/3378335/fe1aa29d-c26d-46d3-b7f3-e9c030ef32c7">

<img width="200" src="https://pyvideotrans.com/images/biancn.jpg">

# Source Code Deployment Issues

1. By default uses ctranslate2 version 4.x, which only supports CUDA12.x version. If your CUDA is lower than 12 and cannot be upgraded to 12.x, please execute commands to uninstall ctranslate2 and reinstall

```

pip uninstall -y ctranslate2

pip install ctranslate2==3.24.0

```

2. You may encounter `xx module not found` errors. Please open requirements.txt, search for the xx module, then remove the == and version number after xx

# Usage Tutorials and Documentation

Please visit https://pyvideotrans.com

# Speech Recognition Models:

   Download link: https://pyvideotrans.com/model.html

# Video Tutorials (Third-party)

[MacOS Source Code Deployment/Bilibili](https://www.bilibili.com/video/BV1tK421y7rd/)

[How to Set Up Video Translation with Gemini API/Bilibili](https://b23.tv/fED1dS3)

[How to Download and Install](https://www.bilibili.com/video/BV1Gr421s7cN/)

# Software Preview Screenshots

![pyvideotrans-home](https://github.com/user-attachments/assets/b2f95a7f-b4e5-4a6d-b2a5-eb6cd22531e0)

![image](https://github.com/user-attachments/assets/b5d1b5fb-c579-477c-bca4-6c5e9aa14d7d)

# Related Projects

[ChatTTS-ui: UI interface for synthesizing speech using ChatTTS](https://github.com/jianchang512/ChatTTS-ui)

[OTT: Local offline text translation tool](https://github.com/jianchang512/ott)

[Voice Cloning Tool: Synthesize speech with any voice](https://github.com/jianchang512/clone-voice)

[Speech Recognition Tool: Local offline speech-to-text tool](https://github.com/jianchang512/stt)

[Vocal and Background Music Separation: Tool for separating vocals and background music](https://github.com/jianchang512/vocal-separate)

[Improved version of GPT-SoVITS api.py](https://github.com/jianchang512/gptsovits-api)

[api.py adapted for CosyVoice](https://github.com/jianchang512/cosyvoice-api)

## Acknowledgments

> Main open source projects this program depends on

1. [ffmpeg](https://github.com/FFmpeg/FFmpeg)
2. [PySide6](https://pypi.org/project/PySide6/)
3. [edge-tts](https://github.com/rany2/edge-tts)
4. [faster-whisper](https://github.com/SYSTRAN/faster-whisper)
5. [openai-whisper](https://github.com/openai/whisper)
6. [pydub](https://github.com/jiaaro/pydub)

## Follow Author's WeChat Official Account

<img width="200" src="https://github.com/jianchang512/pyvideotrans/assets/3378335/f9337111-9084-41fe-8840-1fb8fedca92d">

If you find this project valuable and hope it can be maintained stably and continuously, donations are welcome

<img width="200" src="https://github.com/user-attachments/assets/5e8688ef-47c3-4a3c-a016-e60f73ccc4dc">

<img width="200" src="https://github.com/jianchang512/pyvideotrans/assets/3378335/fe1aa29d-c26d-46d3-b7f3-e9c030ef32c7">

<img width="200" src="https://pyvideotrans.com/images/biancn.jpg">
