#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Enhanced build script specifically for Python 3.10
Script build nâng cao dành riêng cho Python 3.10
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_python_310():
    """Kiểm tra Python 3.10 cụ thể"""
    print("🐍 Checking Python 3.10...")
    
    version = sys.version_info
    print(f"Current Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3 or version.minor != 10:
        print("❌ This script requires Python 3.10 specifically")
        print(f"You are using Python {version.major}.{version.minor}")
        print("\n💡 To use Python 3.10:")
        print("1. Install Python 3.10 from python.org")
        print("2. Use: py -3.10 build_exe_python310.py")
        print("3. Or activate Python 3.10 virtual environment")
        return False
    
    print("✅ Python 3.10 confirmed")
    return True

def install_python310_dependencies():
    """Cài đặt dependencies tối ưu cho Python 3.10"""
    print("\n📦 Installing Python 3.10 optimized dependencies...")
    
    # Dependencies với version tương thích Python 3.10
    dependencies = [
        "pyinstaller>=5.13.0,<6.0",
        "PySide6>=6.5.0,<6.7",
        "setuptools>=65.0.0",
        "wheel>=0.38.0",
        "pip>=22.0.0",
    ]
    
    for dep in dependencies:
        print(f"Installing {dep}...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "--upgrade", dep
            ], check=True, capture_output=True, text=True)
            print(f"✅ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False
    
    return True

def create_python310_spec():
    """Tạo spec file tối ưu cho Python 3.10"""
    print("\n📝 Creating Python 3.10 optimized spec file...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# PyVideoTrans build spec for Python 3.10
import os
import sys
from pathlib import Path

# Current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))
videotrans_path = os.path.join(current_dir, 'videotrans')

# Python 3.10 optimized hidden imports
hidden_imports = [
    # Core videotrans
    'videotrans',
    'videotrans.configure',
    'videotrans.configure.config',
    'videotrans.mainwin',
    'videotrans.mainwin._main_win',
    'videotrans.ui',
    'videotrans.translator',
    'videotrans.task',
    'videotrans.util',
    'videotrans.component',
    'videotrans.winform',
    
    # PySide6 (compatible with Python 3.10)
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtMultimedia',
    'PySide6.QtNetwork',
    'PySide6.QtOpenGL',
    'PySide6.QtPrintSupport',
    'PySide6.QtSvg',
    'PySide6.QtTest',
    'PySide6.QtWebEngineWidgets',
    
    # AI/ML libraries (Python 3.10 compatible)
    'torch',
    'torch.nn',
    'torch.nn.functional',
    'torchaudio',
    'numpy',
    'scipy',
    'librosa',
    'soundfile',
    'faster_whisper',
    'openai',
    'google.generativeai',
    'anthropic',
    
    # Audio/Video processing
    'pydub',
    'cv2',
    'PIL',
    'PIL.Image',
    'moviepy',
    'ffmpeg',
    
    # Network and API
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.exceptions',
    'urllib3',
    'certifi',
    'httpx',
    'aiohttp',
    
    # Text processing
    'srt',
    'zhconv',
    'pysrt',
    'webvtt',
    
    # Cloud services
    'azure.cognitiveservices.speech',
    'google.cloud.texttospeech',
    'elevenlabs',
    'edge_tts',
    
    # Utilities
    'yaml',
    'json',
    'pathlib',
    'tempfile',
    'logging',
    'datetime',
    'locale',
    'threading',
    'multiprocessing',
    'queue',
    'subprocess',
    'shutil',
    'zipfile',
    'tarfile',
    'hashlib',
    'base64',
    'uuid',
    'configparser',
    
    # Python 3.10 specific
    'importlib.metadata',
    'zoneinfo',
    'graphlib',
    'contextlib',
    'functools',
    'itertools',
    'collections',
    'collections.abc',
    'typing',
    'typing_extensions',
    
    # Package management
    'pkg_resources',
    'packaging',
    'packaging.version',
    'packaging.specifiers',
    'packaging.requirements',
]

# Data files
datas = [
    (os.path.join(videotrans_path, 'styles'), 'videotrans/styles'),
    (os.path.join(videotrans_path, 'language'), 'videotrans/language'),
    (os.path.join(videotrans_path, 'ui'), 'videotrans/ui'),
    (os.path.join(videotrans_path, 'prompts'), 'videotrans/prompts'),
    (os.path.join(current_dir, 'voice_list.json'), '.'),
    (os.path.join(current_dir, 'azure_voice_list.json'), '.'),
    (os.path.join(current_dir, 'version.json'), '.'),
]

# Include all resource files
for root, dirs, files in os.walk(videotrans_path):
    for file in files:
        if file.endswith(('.json', '.txt', '.ico', '.png', '.jpg', '.qss', '.ui')):
            full_path = os.path.join(root, file)
            rel_path = os.path.relpath(root, current_dir)
            datas.append((full_path, rel_path))

# Analysis
a = Analysis(
    ['sp.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib.backends._backend_tk',
        'IPython',
        'jupyter',
        'notebook',
        'pandas',
        'scipy.sparse.csgraph._validation',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicates
a.datas = list(set(a.datas))

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='PyVideoTrans',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(videotrans_path, 'styles', 'icon.ico') if os.path.exists(os.path.join(videotrans_path, 'styles', 'icon.ico')) else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PyVideoTrans',
)
'''
    
    try:
        with open('pyvideotrans_py310.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        print("✅ Created Python 3.10 optimized spec file: pyvideotrans_py310.spec")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create spec file: {e}")
        return False

def build_with_python310():
    """Build với Python 3.10"""
    print("\n🔨 Building with Python 3.10...")
    
    # Clean old builds
    for dir_name in ['build', 'dist']:
        if Path(dir_name).exists():
            shutil.rmtree(dir_name)
            print(f"✅ Cleaned {dir_name}/")
    
    # Build command
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        '--log-level=INFO',
        'pyvideotrans_py310.spec'
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        # Run with real-time output
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            universal_newlines=True
        )
        
        # Print output in real-time
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ Build completed successfully!")
            return True
        else:
            print(f"❌ Build failed with return code: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Build failed: {e}")
        return False

def create_distribution_package():
    """Tạo package phân phối"""
    print("\n📦 Creating distribution package...")
    
    dist_dir = Path("dist/PyVideoTrans")
    if not dist_dir.exists():
        print("❌ Build directory not found")
        return False
    
    # Create version info
    version_info = f"""PyVideoTrans - Portable Edition
Built with Python 3.10
Build date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

System Requirements:
- Windows 10/11 (64-bit)
- 4GB RAM minimum (8GB recommended)
- 2GB free disk space

This is a portable version - no installation required.
Simply extract and run PyVideoTrans.exe
"""
    
    with open(dist_dir / "VERSION_INFO.txt", 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    # Create quick start guide
    quick_start = """Quick Start Guide:

1. Double-click PyVideoTrans.exe to start
2. Configure your API keys in Settings
3. Load your video file
4. Select source and target languages
5. Click Start to begin translation

For detailed documentation, visit:
https://pyvideotrans.com
"""
    
    with open(dist_dir / "QUICK_START.txt", 'w', encoding='utf-8') as f:
        f.write(quick_start)
    
    print("✅ Distribution package created")
    return True

def main():
    """Main function"""
    print("🚀 PyVideoTrans Python 3.10 Build Script")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not Path('videotrans').exists() or not Path('sp.py').exists():
        print("❌ Please run this script from the PyVideoTrans root directory")
        return False
    
    # Build steps
    steps = [
        ("Python 3.10 Check", check_python_310),
        ("Install Dependencies", install_python310_dependencies),
        ("Create Spec File", create_python310_spec),
        ("Build Executable", build_with_python310),
        ("Create Distribution", create_distribution_package),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        if not step_func():
            print(f"❌ {step_name} failed")
            return False
    
    # Success summary
    print("\n" + "="*60)
    print("🎉 BUILD COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    exe_path = Path("dist/PyVideoTrans/PyVideoTrans.exe")
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"📊 Executable: {exe_path}")
        print(f"📏 Size: {size_mb:.1f} MB")
    
    print(f"📁 Distribution folder: {Path('dist/PyVideoTrans').absolute()}")
    print("\n✅ Ready for distribution!")
    print("Users can run the executable without Python installed.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
