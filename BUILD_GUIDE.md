# 🚀 PyVideoTrans Build Guide

Hướng dẫn chi tiết để build PyVideoTrans thành file exe không bị lỗi.

## 📋 Y<PERSON>u cầu hệ thống

### Phần mềm cần thiết:
- **Python 3.8+** (khu<PERSON>ến nghị 3.10 hoặc 3.11)
- **Git** (để clone repository)
- **Visual Studio Build Tools** (cho Windows)

### Phần cứng khuyến nghị:
- **RAM**: Tối thiểu 8GB (khuyến nghị 16GB)
- **Ổ cứng**: 10GB dung lượng trống
- **CPU**: Multi-core processor

## 🛠️ Chuẩn bị môi trường

### 1. Clone repository
```bash
git clone https://github.com/jianchang512/pyvideotrans.git
cd pyvideotrans
```

### 2. Tạo virtual environment (khuyến nghị)
```bash
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
```

### 3. Cài đặt dependencies
```bash
pip install --upgrade pip
pip install -r requirements.txt
pip install pyinstaller
```

## 🧪 Kiểm tra trước khi build

### 1. Chạy test suite
```bash
python test_build.py
```

### 2. Fix các vấn đề nếu có
```bash
python fix_build_issues.py
```

### 3. Test chạy ứng dụng
```bash
python sp.py
```

## 🔨 Build Process

### Phương pháp 1: Sử dụng script tự động (Khuyến nghị)
```bash
# Windows
build.bat

# Hoặc chạy trực tiếp
python build_exe.py
```

### Phương pháp 2: Sử dụng PyInstaller trực tiếp
```bash
pyinstaller --clean --noconfirm pyvideotrans.spec
```

### Phương pháp 3: Build minimal để test
```bash
python fix_build_issues.py
# Chọn option test minimal build
```

## 📁 Cấu trúc output

Sau khi build thành công:
```
dist/
└── PyVideoTrans/
    ├── PyVideoTrans.exe     # File thực thi chính
    ├── _internal/           # Thư viện và dependencies
    ├── run.bat             # Script launcher
    ├── README.txt          # Hướng dẫn cho người dùng
    └── [các file khác]
```

## 🐛 Xử lý lỗi thường gặp

### 1. Lỗi "Module not found"
```bash
# Cài đặt lại dependencies
pip install --force-reinstall -r requirements.txt

# Hoặc cài đặt từng package
pip install PySide6 torch numpy requests
```

### 2. Lỗi PyTorch
```bash
# Cài đặt PyTorch CPU version
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
```

### 3. Lỗi PySide6
```bash
# Cài đặt lại PySide6
pip uninstall PySide6 shiboken6
pip install PySide6
```

### 4. Lỗi "Permission denied"
- Chạy terminal với quyền Administrator
- Tắt antivirus tạm thời
- Thêm thư mục vào whitelist của Windows Defender

### 5. Lỗi "Out of memory"
- Đóng các ứng dụng khác
- Tăng virtual memory
- Sử dụng `--exclude` để loại bỏ packages không cần thiết

## ⚡ Tối ưu hóa build

### 1. Giảm kích thước file
```python
# Trong file .spec, thêm vào excludes:
excludes=[
    'matplotlib',
    'scipy', 
    'pandas',
    'jupyter',
    'tkinter',
    'unittest',
    'pytest'
]
```

### 2. Tăng tốc độ build
```bash
# Sử dụng cache
pyinstaller --clean --noconfirm --log-level=WARN pyvideotrans.spec
```

### 3. Debug build issues
```bash
# Build với console để xem lỗi
# Trong .spec file: console=True
pyinstaller --debug=all pyvideotrans.spec
```

## 📊 Thông số build thành công

### Kích thước file:
- **Executable**: ~100MB
- **Total package**: ~2-3GB
- **Compressed**: ~800MB-1GB

### Thời gian build:
- **Máy yếu**: 15-30 phút
- **Máy mạnh**: 5-15 phút

### Compatibility:
- **Windows**: 10/11 64-bit
- **Python**: 3.8-3.11
- **Architecture**: x64

## 🔧 Scripts hỗ trợ

### 1. `test_build.py`
- Kiểm tra dependencies
- Validate cấu trúc project
- Test PyInstaller compatibility

### 2. `fix_build_issues.py`
- Tự động fix các lỗi thường gặp
- Cài đặt missing packages
- Tạo minimal build để test

### 3. `build_exe.py`
- Script build chính với error handling
- Tạo launcher và README
- Optimize build process

### 4. `build.bat`
- Wrapper script cho Windows
- Kiểm tra Python installation
- Mở output folder sau khi build

## 📝 Checklist trước khi build

- [ ] Python 3.8+ đã cài đặt
- [ ] Virtual environment đã tạo và activate
- [ ] Dependencies đã cài đặt đầy đủ
- [ ] Test suite đã pass
- [ ] Ứng dụng chạy được bằng `python sp.py`
- [ ] Đủ dung lượng ổ cứng (10GB+)
- [ ] Đủ RAM (8GB+)
- [ ] Antivirus đã tắt/whitelist

## 🎯 Tips để build thành công

1. **Sử dụng Python 3.10**: Ổn định nhất cho PyInstaller
2. **Virtual environment**: Tránh conflict dependencies
3. **Clean build**: Luôn dùng `--clean` flag
4. **Test trước**: Chạy `test_build.py` trước khi build
5. **Monitor resources**: Theo dõi RAM và CPU usage
6. **Patience**: Build có thể mất 15-30 phút

## 🆘 Hỗ trợ

Nếu gặp vấn đề:

1. **Chạy diagnostic**: `python fix_build_issues.py`
2. **Check logs**: Xem output của PyInstaller
3. **Search issues**: GitHub issues của project
4. **Ask community**: Discord/Forum của project

## 📚 Tài liệu tham khảo

- [PyInstaller Documentation](https://pyinstaller.readthedocs.io/)
- [PySide6 Documentation](https://doc.qt.io/qtforpython/)
- [PyTorch Installation](https://pytorch.org/get-started/locally/)
- [PyVideoTrans GitHub](https://github.com/jianchang512/pyvideotrans)

---

**Chúc bạn build thành công! 🎉**
