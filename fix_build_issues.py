#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script để fix các lỗi thường gặp khi build PyVideoTrans
Fix common build issues for PyVideoTrans
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def fix_torch_issues():
    """Fix các vấn đề liên quan đến PyTorch"""
    print("🔧 Fixing PyTorch issues...")
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        
        # Kiểm tra CUDA availability
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.version.cuda}")
        else:
            print("⚠️  CUDA not available (CPU-only mode)")
        
        return True
    except ImportError:
        print("❌ PyTorch not found. Installing CPU version...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "torch", "torchaudio", "--index-url", 
                "https://download.pytorch.org/whl/cpu"
            ])
            print("✅ PyTorch CPU version installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyTorch")
            return False

def fix_pyside6_issues():
    """Fix các vấn đề liên quan đến PySide6"""
    print("\n🔧 Fixing PySide6 issues...")
    
    try:
        import PySide6
        from PySide6 import QtCore, QtGui, QtWidgets
        print(f"✅ PySide6 version: {PySide6.__version__}")
        return True
    except ImportError:
        print("❌ PySide6 not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6"])
            print("✅ PySide6 installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PySide6")
            return False

def fix_missing_dependencies():
    """Fix các dependencies bị thiếu"""
    print("\n🔧 Checking and fixing missing dependencies...")
    
    # Danh sách các packages quan trọng
    critical_packages = [
        'requests',
        'numpy',
        'Pillow',
        'pydub',
        'soundfile',
        'librosa',
        'faster-whisper',
        'openai-whisper',
        'edge-tts',
        'zhconv',
        'srt',
        'pytz',
        'PyYAML',
        'pydantic',
        'certifi',
        'httpx',
        'aiohttp'
    ]
    
    missing_packages = []
    
    for package in critical_packages:
        try:
            # Thử import với tên package đã normalize
            import_name = package.replace('-', '_').replace('_', '').lower()
            if import_name == 'pillow':
                import_name = 'PIL'
            elif import_name == 'pyyaml':
                import_name = 'yaml'
            elif import_name == 'fasterwhisper':
                import_name = 'faster_whisper'
            elif import_name == 'openaiwhisper':
                import_name = 'whisper'
            elif import_name == 'edgetts':
                import_name = 'edge_tts'
            
            __import__(import_name)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages)
            print("✅ Missing packages installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install some packages")
            return False
    
    return True

def fix_path_issues():
    """Fix các vấn đề về đường dẫn"""
    print("\n🔧 Fixing path issues...")
    
    # Kiểm tra các file quan trọng
    important_files = [
        'sp.py',
        'videotrans/__init__.py',
        'videotrans/configure/config.py',
        'videotrans/styles/icon.ico',
        'videotrans/styles/logo.png',
        'videotrans/styles/style.qss'
    ]
    
    missing_files = []
    for file_path in important_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ Missing: {file_path}")
        else:
            print(f"✅ Found: {file_path}")
    
    if missing_files:
        print(f"\n⚠️  Missing critical files: {missing_files}")
        return False
    
    return True

def fix_pyinstaller_issues():
    """Fix các vấn đề liên quan đến PyInstaller"""
    print("\n🔧 Fixing PyInstaller issues...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
        
        # Kiểm tra version compatibility
        version_parts = PyInstaller.__version__.split('.')
        major_version = int(version_parts[0])
        
        if major_version < 5:
            print("⚠️  PyInstaller version is old. Upgrading...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "--upgrade", "pyinstaller"
            ])
            print("✅ PyInstaller upgraded")
        
        return True
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "pyinstaller"
            ])
            print("✅ PyInstaller installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install PyInstaller")
            return False

def create_minimal_spec():
    """Tạo file spec tối giản để test"""
    print("\n📝 Creating minimal spec file for testing...")
    
    minimal_spec = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['sp.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('videotrans/styles', 'videotrans/styles'),
        ('videotrans/language', 'videotrans/language'),
        ('videotrans/ui', 'videotrans/ui'),
    ],
    hiddenimports=[
        'videotrans',
        'videotrans.configure.config',
        'videotrans.mainwin._main_win',
        'PySide6.QtCore',
        'PySide6.QtGui',
        'PySide6.QtWidgets',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='PyVideoTrans_minimal',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Enable console for debugging
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PyVideoTrans_minimal',
)
'''
    
    with open('minimal_test.spec', 'w', encoding='utf-8') as f:
        f.write(minimal_spec)
    
    print("✅ Created minimal_test.spec")

def test_minimal_build():
    """Test build với spec tối giản"""
    print("\n🧪 Testing minimal build...")
    
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--noconfirm',
        'minimal_test.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Minimal build successful!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Minimal build failed: {e.returncode}")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main fix process"""
    print("🔧 PyVideoTrans Build Issue Fixer")
    print("=" * 50)
    
    all_fixes_successful = True
    
    # Fix các vấn đề từng bước
    fixes = [
        ("Path Issues", fix_path_issues),
        ("PyTorch Issues", fix_torch_issues),
        ("PySide6 Issues", fix_pyside6_issues),
        ("Missing Dependencies", fix_missing_dependencies),
        ("PyInstaller Issues", fix_pyinstaller_issues),
    ]
    
    for fix_name, fix_function in fixes:
        print(f"\n{'='*20} {fix_name} {'='*20}")
        if not fix_function():
            print(f"❌ {fix_name} failed")
            all_fixes_successful = False
        else:
            print(f"✅ {fix_name} completed")
    
    if all_fixes_successful:
        print("\n🎉 All fixes completed successfully!")
        print("You can now try building with: python build_exe.py")
        
        # Tạo minimal spec để test
        create_minimal_spec()
        
        # Test minimal build
        print("\nWould you like to test a minimal build? (y/n): ", end="")
        response = input().strip().lower()
        if response == 'y':
            test_minimal_build()
    else:
        print("\n❌ Some fixes failed. Please resolve the issues above.")
    
    return all_fixes_successful

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
